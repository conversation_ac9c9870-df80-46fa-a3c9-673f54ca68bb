#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试脚本
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.getcwd())

try:
    from src.document_generator import DocumentGenerator
    print("✓ DocumentGenerator 导入成功")
except ImportError as e:
    print(f"✗ DocumentGenerator 导入失败: {e}")

try:
    from src.data_processor import DataProcessor
    print("✓ DataProcessor 导入成功")
except ImportError as e:
    print(f"✗ DataProcessor 导入失败: {e}")

try:
    from src.scraper import ZhaoZhongAIScraper
    print("✓ ZhaoZhongAIScraper 导入成功")
except ImportError as e:
    print(f"✗ ZhaoZhongAIScraper 导入失败: {e}")

# 测试基本功能
print("\n测试基本功能...")

# 创建模拟数据
test_products = [
    {
        'id': 'test_001',
        'name': '测试传感器',
        'category': '测试分类',
        'description': '这是一个测试传感器',
        'price': {'formatted': '¥100'},
        'technical_specs': {'精度': '±1%', '量程': '0-100'},
        'applications': ['测试应用'],
        'features': ['高精度', '稳定'],
        'target_industries': ['测试行业'],
        'key_selling_points': ['性价比高']
    }
]

test_categories = {
    '测试分类': {
        'name': '测试分类',
        'count': 1,
        'products': test_products
    }
}

test_specs = {
    'all_specs': {},
    'analysis': {}
}

try:
    # 测试数据处理器
    processor = DataProcessor()
    print("✓ DataProcessor 实例化成功")
    
    # 测试文档生成器
    generator = DocumentGenerator(test_products, test_categories, test_specs)
    print("✓ DocumentGenerator 实例化成功")
    
    print("\n所有基本测试通过！")
    
except Exception as e:
    print(f"✗ 测试失败: {e}")
    import traceback
    traceback.print_exc()