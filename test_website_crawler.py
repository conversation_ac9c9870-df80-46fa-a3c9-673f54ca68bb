#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
<AUTHOR> 
@license          http://www.178188.xyz
@lastmodify       2025年8月1日
模块说明: 赵中AI网站爬虫功能完整测试脚本
"""

import asyncio
import logging
import json
from pathlib import Path
from datetime import datetime
from src.scraper import ZhaoZhongAIScraper

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 目标产品分类列表
TARGET_CATEGORIES = [
    "至璨 · CPU单元",
    "至璨 · 总线耦合器",
    "至璨 · IO模块", 
    "至璨 · 接近传感器",
    "至璨 · 光电传感器",
    "至璨 · 继电器",
    "至璨 · 激光光电传感器",
    "至璨 · 激光位移传感器",
    "至璨 · 色标传感器",
    "至璨 · 光纤传感器",
    "至璨 · 微动/行程/限位开关",
    "至璨 · 管道液位传感器"
]

# 目标网站URL
TARGET_URLS = {
    "主网站": "https://www.zhaozhongai.com/",
    "产品中心": "https://www.zhaozhongai.com/product/",
    "下载中心": "https://www.zhaozhongai.com/download/"
}

class WebsiteCrawlerTester:
    """网站爬虫测试器"""
    
    def __init__(self):
        self.scraper = None
        self.test_results = {
            "website_access": {},
            "category_detection": {},
            "product_extraction": {},
            "data_quality": {},
            "errors": []
        }
        
    async def initialize(self):
        """初始化爬虫"""
        logger.info("初始化网站爬虫测试器...")
        self.scraper = ZhaoZhongAIScraper()
        await self.scraper.initialize()
        
    async def cleanup(self):
        """清理资源"""
        if self.scraper:
            await self.scraper.close()
            
    async def test_website_access(self):
        """测试网站访问功能"""
        logger.info("=" * 50)
        logger.info("测试1: 网站访问功能")
        logger.info("=" * 50)
        
        for name, url in TARGET_URLS.items():
            try:
                logger.info(f"测试访问 {name}: {url}")
                await self.scraper.page.goto(url, wait_until='networkidle', timeout=30000)
                
                # 获取页面标题
                title = await self.scraper.page.title()
                logger.info(f"✓ {name} 访问成功 - 页面标题: {title}")
                
                self.test_results["website_access"][name] = {
                    "status": "success",
                    "url": url,
                    "title": title
                }
                
                # 等待一下避免请求过快
                await asyncio.sleep(2)
                
            except Exception as e:
                error_msg = f"✗ {name} 访问失败: {str(e)}"
                logger.error(error_msg)
                self.test_results["website_access"][name] = {
                    "status": "failed",
                    "url": url,
                    "error": str(e)
                }
                self.test_results["errors"].append(error_msg)
                
    async def test_category_detection(self):
        """测试产品分类识别功能"""
        logger.info("=" * 50)
        logger.info("测试2: 产品分类识别功能")
        logger.info("=" * 50)
        
        try:
            # 访问产品中心页面
            await self.scraper.page.goto(TARGET_URLS["产品中心"], wait_until='networkidle', timeout=30000)
            
            # 获取产品分类
            categories = await self.scraper.get_product_categories()
            logger.info(f"发现 {len(categories)} 个产品分类:")
            
            found_categories = []
            missing_categories = []
            
            for category_name, category_url in categories.items():
                logger.info(f"  - {category_name}: {category_url}")
                found_categories.append(category_name)
                
            # 检查目标分类是否都被找到
            for target_category in TARGET_CATEGORIES:
                if not any(target_category in found_cat for found_cat in found_categories):
                    missing_categories.append(target_category)
                    
            self.test_results["category_detection"] = {
                "total_found": len(categories),
                "found_categories": found_categories,
                "target_categories": TARGET_CATEGORIES,
                "missing_categories": missing_categories,
                "categories_data": categories
            }
            
            if missing_categories:
                logger.warning(f"未找到的目标分类: {missing_categories}")
            else:
                logger.info("✓ 所有目标分类都已找到")
                
        except Exception as e:
            error_msg = f"产品分类识别失败: {str(e)}"
            logger.error(error_msg)
            self.test_results["errors"].append(error_msg)
            
    async def test_product_extraction(self):
        """测试产品数据提取功能"""
        logger.info("=" * 50)
        logger.info("测试3: 产品数据提取功能")
        logger.info("=" * 50)
        
        categories = self.test_results["category_detection"].get("categories_data", {})
        
        if not categories:
            logger.error("没有可用的产品分类，跳过产品提取测试")
            return
            
        # 测试前3个分类的产品提取
        test_categories = list(categories.items())[:3]
        extraction_results = {}
        
        for category_name, category_url in test_categories:
            try:
                logger.info(f"测试分类: {category_name}")
                
                # 爬取该分类的产品（限制数量）
                products = await self.scraper.scrape_category_products(category_name, category_url)
                
                logger.info(f"  从 '{category_name}' 提取到 {len(products)} 个产品")
                
                # 分析产品数据质量
                quality_analysis = self.analyze_product_quality(products)
                
                extraction_results[category_name] = {
                    "product_count": len(products),
                    "products": products[:2],  # 只保存前2个产品作为样本
                    "quality": quality_analysis
                }
                
                if products:
                    logger.info(f"  示例产品: {products[0].get('name', '未知')}")
                    
            except Exception as e:
                error_msg = f"分类 '{category_name}' 产品提取失败: {str(e)}"
                logger.error(error_msg)
                self.test_results["errors"].append(error_msg)
                
        self.test_results["product_extraction"] = extraction_results
        
    def analyze_product_quality(self, products):
        """分析产品数据质量"""
        if not products:
            return {"status": "no_products"}
            
        total_products = len(products)
        quality_metrics = {
            "has_name": 0,
            "has_description": 0,
            "has_price": 0,
            "has_technical_specs": 0,
            "has_applications": 0,
            "has_features": 0,
            "has_images": 0
        }
        
        for product in products:
            if product.get('name'):
                quality_metrics["has_name"] += 1
            if product.get('description'):
                quality_metrics["has_description"] += 1
            if product.get('price'):
                quality_metrics["has_price"] += 1
            if product.get('technical_specs'):
                quality_metrics["has_technical_specs"] += 1
            if product.get('applications'):
                quality_metrics["has_applications"] += 1
            if product.get('features'):
                quality_metrics["has_features"] += 1
            if product.get('images'):
                quality_metrics["has_images"] += 1
                
        # 计算百分比
        quality_percentages = {}
        for key, count in quality_metrics.items():
            quality_percentages[key] = round((count / total_products) * 100, 2)
            
        return {
            "total_products": total_products,
            "metrics": quality_metrics,
            "percentages": quality_percentages
        }
        
    async def generate_test_report(self):
        """生成测试报告"""
        logger.info("=" * 50)
        logger.info("生成爬虫功能测试报告")
        logger.info("=" * 50)
        
        # 创建报告目录
        report_dir = Path("crawler_test_reports")
        report_dir.mkdir(exist_ok=True)
        
        # 保存详细测试结果
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        results_file = report_dir / f"crawler_test_results_{timestamp}.json"
        
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, ensure_ascii=False, indent=2)
            
        # 生成Markdown报告
        report_content = self.create_markdown_report()
        report_file = report_dir / f"crawler_test_report_{timestamp}.md"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)
            
        logger.info(f"测试报告已保存:")
        logger.info(f"  - 详细结果: {results_file}")
        logger.info(f"  - 报告文档: {report_file}")
        
        return report_file
        
    def create_markdown_report(self):
        """创建Markdown格式的测试报告"""
        report = []
        report.append("# 赵中AI网站爬虫功能测试报告")
        report.append(f"\n**测试时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 网站访问测试结果
        report.append("\n## 1. 网站访问测试")
        for name, result in self.test_results["website_access"].items():
            status = "✅" if result["status"] == "success" else "❌"
            report.append(f"- {status} {name}: {result['url']}")
            if result["status"] == "success":
                report.append(f"  - 页面标题: {result.get('title', '未获取')}")
            else:
                report.append(f"  - 错误: {result.get('error', '未知错误')}")
                
        # 分类识别测试结果
        report.append("\n## 2. 产品分类识别测试")
        category_data = self.test_results.get("category_detection", {})
        if category_data:
            report.append(f"- 发现分类总数: {category_data.get('total_found', 0)}")
            report.append(f"- 目标分类总数: {len(TARGET_CATEGORIES)}")
            
            missing = category_data.get('missing_categories', [])
            if missing:
                report.append(f"- ❌ 未找到的分类: {', '.join(missing)}")
            else:
                report.append("- ✅ 所有目标分类都已找到")
                
        # 产品提取测试结果
        report.append("\n## 3. 产品数据提取测试")
        extraction_data = self.test_results.get("product_extraction", {})
        for category, data in extraction_data.items():
            report.append(f"\n### {category}")
            report.append(f"- 提取产品数量: {data['product_count']}")
            
            quality = data.get('quality', {})
            if quality.get('percentages'):
                report.append("- 数据质量分析:")
                for field, percentage in quality['percentages'].items():
                    report.append(f"  - {field}: {percentage}%")
                    
        # 错误汇总
        if self.test_results["errors"]:
            report.append("\n## 4. 错误汇总")
            for error in self.test_results["errors"]:
                report.append(f"- ❌ {error}")
        else:
            report.append("\n## 4. 测试结果")
            report.append("- ✅ 所有测试通过，未发现错误")
            
        return '\n'.join(report)

async def main():
    """主测试函数"""
    logger.info("开始赵中AI网站爬虫功能完整测试...")
    
    tester = WebsiteCrawlerTester()
    
    try:
        await tester.initialize()
        
        # 执行各项测试
        await tester.test_website_access()
        await tester.test_category_detection()
        await tester.test_product_extraction()
        
        # 生成测试报告
        report_file = await tester.generate_test_report()
        
        logger.info("🎉 爬虫功能测试完成！")
        logger.info(f"📄 查看详细报告: {report_file}")
        
    except Exception as e:
        logger.error(f"测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        
    finally:
        await tester.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
