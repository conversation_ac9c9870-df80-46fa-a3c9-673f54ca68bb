#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
<AUTHOR> 
@license          http://www.178188.xyz
@lastmodify       2025年8月1日
模块说明: 依赖安装脚本
"""

import subprocess
import sys
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def install_package(package):
    """安装Python包"""
    try:
        logger.info(f"安装 {package}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        logger.info(f"✅ {package} 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ {package} 安装失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("开始安装项目依赖...")
    
    # 必需的包列表
    required_packages = [
        "playwright",
        "pandas", 
        "openpyxl",
        "PyPDF2",
        "requests",
        "asyncio"
    ]
    
    failed_packages = []
    
    for package in required_packages:
        if not install_package(package):
            failed_packages.append(package)
    
    # 安装playwright浏览器
    try:
        logger.info("安装Playwright浏览器...")
        subprocess.check_call([sys.executable, "-m", "playwright", "install", "chromium"])
        logger.info("✅ Playwright浏览器安装成功")
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ Playwright浏览器安装失败: {e}")
        failed_packages.append("playwright-browsers")
    
    if failed_packages:
        logger.error(f"以下包安装失败: {failed_packages}")
        logger.error("请手动安装这些包")
        sys.exit(1)
    else:
        logger.info("🎉 所有依赖安装完成！")
        logger.info("现在可以运行 python run_complete_system.py")

if __name__ == "__main__":
    main()
