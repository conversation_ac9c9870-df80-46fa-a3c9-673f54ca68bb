#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
<AUTHOR> 
@license          http://www.178188.xyz
@lastmodify       2025年8月1日
模块说明: 文档生成功能完整测试脚本
"""

import asyncio
import logging
from pathlib import Path
from src.data_processor import DataProcessor
from src.document_generator import DocumentGenerator

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_comprehensive_mock_data():
    """创建完整的模拟数据用于测试"""
    return [
        {
            'name': '温度传感器 TH-001',
            'category': '温度传感器',
            'description': '高精度数字温度传感器，适用于工业环境监测，具有优异的稳定性和可靠性',
            'price': '¥299',
            'url': 'https://example.com/th001',
            'images': ['https://example.com/th001.jpg'],
            'technical_specs': {
                '测量范围': '-40°C ~ +85°C',
                '精度': '±0.5°C',
                '分辨率': '0.1°C',
                '工作电压': '3.3V ~ 5V',
                '输出信号': 'I2C数字信号',
                '工作温度': '-20°C ~ +70°C',
                '防护等级': 'IP65'
            },
            'applications': ['工业自动化', '环境监测', '智能家居', '温室大棚'],
            'features': ['高精度测量', '数字输出', '低功耗设计', '宽温度范围', '防水防尘'],
            'scraped_at': 1234567890
        },
        {
            'name': '压力传感器 PR-002',
            'category': '压力传感器',
            'description': '工业级压力传感器，适用于液体和气体压力测量，抗冲击性能优异',
            'price': '¥599',
            'url': 'https://example.com/pr002',
            'images': ['https://example.com/pr002.jpg'],
            'technical_specs': {
                '测量范围': '0 ~ 10MPa',
                '精度': '±0.25%FS',
                '输出信号': '4-20mA',
                '工作温度': '-20°C ~ +80°C',
                '供电电压': '12-36VDC',
                '响应时间': '<1ms',
                '防护等级': 'IP67'
            },
            'applications': ['工业自动化', '流体控制', '安全监测', '石油化工'],
            'features': ['高稳定性', '抗冲击', '防腐蚀', '快速响应', '宽电压供电'],
            'scraped_at': 1234567890
        },
        {
            'name': '距离传感器 DS-003',
            'category': '距离传感器',
            'description': '激光测距传感器，高精度非接触式距离测量，适用于自动化设备',
            'price': '¥899',
            'url': 'https://example.com/ds003',
            'images': ['https://example.com/ds003.jpg'],
            'technical_specs': {
                '测量范围': '0.1m ~ 100m',
                '精度': '±2mm',
                '分辨率': '1mm',
                '激光等级': 'Class 2',
                '工作电压': '12-24VDC',
                '输出信号': 'RS485/Modbus',
                '防护等级': 'IP65'
            },
            'applications': ['自动化生产线', '物料检测', '车辆检测', '安防监控'],
            'features': ['激光测距', '高精度', '非接触测量', '数字通信', '抗干扰'],
            'scraped_at': 1234567890
        },
        {
            'name': '湿度传感器 HM-004',
            'category': '湿度传感器',
            'description': '数字式温湿度传感器，集成温度和湿度测量功能',
            'price': '¥199',
            'url': 'https://example.com/hm004',
            'images': ['https://example.com/hm004.jpg'],
            'technical_specs': {
                '湿度范围': '0-100%RH',
                '湿度精度': '±2%RH',
                '温度范围': '-40°C ~ +80°C',
                '温度精度': '±0.3°C',
                '工作电压': '3.3V',
                '输出信号': 'I2C',
                '响应时间': '8s'
            },
            'applications': ['环境监测', '智能家居', '农业大棚', '仓储管理'],
            'features': ['温湿度一体', '数字输出', '低功耗', '快速响应', '小体积'],
            'scraped_at': 1234567890
        },
        {
            'name': '气体传感器 GS-005',
            'category': '气体传感器',
            'description': '多气体检测传感器，可检测CO、CO2、NH3等多种气体',
            'price': '¥1299',
            'url': 'https://example.com/gs005',
            'images': ['https://example.com/gs005.jpg'],
            'technical_specs': {
                '检测气体': 'CO, CO2, NH3, H2S',
                'CO检测范围': '0-1000ppm',
                'CO2检测范围': '0-5000ppm',
                '精度': '±5%读数',
                '工作温度': '-10°C ~ +50°C',
                '输出信号': 'RS485',
                '预热时间': '30s'
            },
            'applications': ['环境监测', '工业安全', '智能建筑', '地下空间'],
            'features': ['多气体检测', '高灵敏度', '数字通信', '报警功能', '长寿命'],
            'scraped_at': 1234567890
        }
    ]

async def test_complete_document_generation():
    """测试完整的文档生成功能"""
    logger.info("开始完整文档生成测试...")
    
    try:
        # 1. 创建模拟数据
        mock_data = create_comprehensive_mock_data()
        logger.info(f"创建了 {len(mock_data)} 个模拟产品数据")
        
        # 2. 数据处理
        processor = DataProcessor()
        clean_data = processor.clean_and_structure_data(mock_data)
        categories = processor.categorize_products(clean_data)
        tech_specs = processor.extract_technical_specifications(clean_data)
        
        logger.info(f"数据处理完成:")
        logger.info(f"  - 清洗后产品数: {len(clean_data)}")
        logger.info(f"  - 分类数: {len(categories)}")
        logger.info(f"  - 技术规格分析: {len(tech_specs.get('analysis', {}))}")
        
        # 3. 创建输出目录
        output_dir = Path("test_output")
        output_dir.mkdir(exist_ok=True)
        
        # 4. 文档生成
        generator = DocumentGenerator(clean_data, categories, tech_specs)
        await generator.generate_all_documents(output_dir)
        
        # 5. 验证生成的文件
        generated_files = list(output_dir.glob("*"))
        logger.info(f"成功生成 {len(generated_files)} 个文件:")
        
        for file in generated_files:
            file_size = file.stat().st_size
            logger.info(f"  - {file.name} ({file_size} bytes)")
            
            # 验证文件内容不为空
            if file_size == 0:
                logger.warning(f"警告: {file.name} 文件为空")
            else:
                logger.info(f"✓ {file.name} 文件生成成功")
        
        # 6. 检查预期的文件是否都生成了
        expected_files = [
            "产品分类手册.md",
            "客户需求快速匹配指南.md", 
            "产品推荐速查表.xlsx",
            "标准销售话术库.md"
        ]
        
        missing_files = []
        for expected_file in expected_files:
            if not (output_dir / expected_file).exists():
                missing_files.append(expected_file)
        
        if missing_files:
            logger.error(f"缺少预期文件: {missing_files}")
        else:
            logger.info("✓ 所有预期文件都已生成")
        
        logger.info("文档生成测试完成！")
        return True
        
    except Exception as e:
        logger.error(f"文档生成测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主测试函数"""
    logger.info("开始销售工具文档生成系统测试...")
    
    success = await test_complete_document_generation()
    
    if success:
        logger.info("🎉 所有测试通过！销售工具文档生成系统运行正常。")
        print("\n" + "="*50)
        print("测试结果: ✅ 成功")
        print("生成的文件位于: test_output/ 目录")
        print("="*50)
    else:
        logger.error("❌ 测试失败！请检查错误信息。")
        print("\n" + "="*50)
        print("测试结果: ❌ 失败")
        print("="*50)

if __name__ == "__main__":
    asyncio.run(main())
