# 销售人员产品匹配爬虫系统

## 项目简介

这是一个专为销售人员设计的产品匹配系统，能够自动爬取赵中AI网站（https://www.zhaozhongai.com/down/）的产品资料，解析PDF文档内容，并生成专业的销售支持文档。

## 主要功能

### 🕷️ 智能爬虫功能
- 自动爬取赵中AI资料下载页面
- 识别产品分类和子分类结构
- 下载并解析PDF技术文档
- 提取产品技术规格、应用场景、产品特点

### 📊 数据处理功能
- 结构化存储产品信息
- 智能提取关键技术参数
- 分析产品应用场景
- 生成竞争优势描述

### 📝 文档生成功能
- 完整产品目录
- 销售人员推荐指南
- 行业特定推荐指南
- 产品对比表（Excel格式）
- 客户需求匹配指南
- 专业销售话术库

## 系统架构

```
├── src/
│   ├── zhaozhong_scraper_v2.py    # 爬虫核心模块
│   └── document_generator.py       # 文档生成模块
├── test_zhaozhong_v2.py           # 爬虫测试脚本
├── run_complete_system.py         # 完整系统运行脚本
├── install_dependencies.py        # 依赖安装脚本
└── README.md                      # 项目说明
```

## 快速开始

### 1. 安装依赖

```bash
python install_dependencies.py
```

### 2. 运行完整系统

```bash
# 完整运行（爬取所有数据并生成文档）
python run_complete_system.py

# 测试模式（少量数据测试）
python run_complete_system.py --test-mode
```

### 3. 单独测试爬虫

```bash
# 测试单个分类
python test_zhaozhong_v2.py --mode single

# 完整爬取测试
python test_zhaozhong_v2.py --mode full
```

## 输出文件说明

运行完成后，系统会在 `output/YYYYMMDD_HHMMSS/` 目录下生成以下文件：

### 📋 销售支持文档
- `完整产品目录.md` - 所有产品的详细信息
- `销售人员产品推荐指南.md` - 按应用场景分类的推荐
- `工业自动化_产品推荐指南.md` - 行业特定指南
- `智能制造_产品推荐指南.md` - 行业特定指南
- `环境监测_产品推荐指南.md` - 行业特定指南
- `建筑智能化_产品推荐指南.md` - 行业特定指南

### 📊 数据文件
- `产品对比表.xlsx` - Excel格式的产品对比表
- `客户需求快速匹配指南.md` - 客户需求分析方法
- `专业销售话术库.md` - 基于实际产品的销售话术

### 🔧 技术文件
- `products_data.json` - 结构化的产品数据
- `raw_products_data.json` - 原始爬取数据
- `系统运行报告.md` - 详细的运行结果报告

## 核心特性

### 🎯 精准数据提取
- **PDF内容解析**: 自动下载并解析产品PDF文档
- **智能参数识别**: 识别电压、电流、精度等关键技术参数
- **应用场景提取**: 从文档中提取产品应用场景
- **特点总结**: 自动提取产品核心特点和优势

### 🏢 行业化推荐
- **多行业覆盖**: 支持工业自动化、智能制造等多个行业
- **场景化匹配**: 根据应用场景推荐最适合的产品
- **竞争优势分析**: 自动生成产品竞争优势描述

### 💬 专业话术生成
- **开场白话术**: 基于产品数量和应用领域的专业开场
- **产品介绍**: 基于实际技术参数的产品介绍话术
- **异议处理**: 针对价格、技术等常见异议的应对话术
- **成交促进**: 紧迫性营造和风险消除话术

## 技术特点

### 🚀 高性能爬虫
- 使用Playwright进行动态网页爬取
- 智能等待和重试机制
- 反爬虫检测规避
- 并发处理提高效率

### 🧠 智能数据处理
- 正则表达式模式匹配
- 关键词优先级排序
- 数据去重和清洗
- 结构化数据存储

### 📈 数据质量保证
- 数据完整性评分
- 质量统计分析
- 错误处理和日志记录
- 数据验证机制

## 使用场景

### 👨‍💼 销售人员
- 快速了解产品线
- 根据客户需求推荐产品
- 使用标准话术提高成交率
- 产品对比和竞争分析

### 📊 销售管理
- 产品数据统一管理
- 销售培训材料准备
- 市场分析和产品规划
- 客户需求趋势分析

### 🎯 市场营销
- 产品宣传材料制作
- 行业解决方案包装
- 竞争对手分析
- 市场定位策略

## 注意事项

1. **网络要求**: 需要稳定的网络连接访问目标网站
2. **运行时间**: 完整爬取可能需要较长时间，请耐心等待
3. **数据更新**: 建议定期运行以获取最新产品信息
4. **合规使用**: 请遵守网站使用条款，合理控制爬取频率

## 故障排除

### 常见问题

**Q: 爬虫运行失败怎么办？**
A: 检查网络连接，确认目标网站可访问，查看日志文件了解具体错误

**Q: PDF解析失败？**
A: 可能是PDF格式问题，系统会跳过无法解析的文件并继续处理

**Q: 生成的文档内容不完整？**
A: 检查数据完整性评分，可能需要优化提取算法或补充数据

### 日志文件
- `complete_system.log` - 完整系统运行日志
- `zhaozhong_scraper_v2.log` - 爬虫运行日志

## 更新日志

### v2.0 (2025-08-01)
- ✅ 重新设计爬虫架构，正确处理资料下载页面
- ✅ 实现PDF内容解析和数据提取
- ✅ 完善销售文档生成功能
- ✅ 添加数据质量评估和统计
- ✅ 优化用户体验和错误处理

## 技术支持

如有问题或建议，请联系：
- 作者：苏昱
- 许可：http://www.178188.xyz

---

**搞完了** 🎉
