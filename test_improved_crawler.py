#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
<AUTHOR> 
@license          http://www.178188.xyz
@lastmodify       2025年8月1日
模块说明: 改进后的爬虫功能测试脚本
"""

import asyncio
import logging
import json
from pathlib import Path
from datetime import datetime
from src.scraper import <PERSON><PERSON>hongAIScraper
from src.data_processor import DataProcessor
from src.document_generator import DocumentGenerator

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ImprovedCrawlerTester:
    """改进后的爬虫测试器"""
    
    def __init__(self):
        self.scraper = None
        self.test_results = {
            "category_test": {},
            "product_extraction_test": {},
            "data_quality_test": {},
            "document_generation_test": {},
            "summary": {}
        }
        
    async def initialize(self):
        """初始化爬虫"""
        logger.info("初始化改进后的爬虫测试器...")
        self.scraper = Zhao<PERSON>hongAIScraper()
        await self.scraper.initialize()
        
    async def cleanup(self):
        """清理资源"""
        if self.scraper:
            await self.scraper.close()
            
    async def test_category_recognition(self):
        """测试分类识别功能"""
        logger.info("=" * 60)
        logger.info("测试1: 改进后的分类识别功能")
        logger.info("=" * 60)
        
        try:
            # 访问产品中心页面
            await self.scraper.page.goto("https://www.zhaozhongai.com/product/", 
                                        wait_until='networkidle', timeout=30000)
            
            # 获取产品分类
            categories = await self.scraper.get_product_categories()
            
            logger.info(f"✓ 成功识别 {len(categories)} 个产品分类:")
            for category_name, category_url in categories.items():
                logger.info(f"  - {category_name}: {category_url}")
                
            self.test_results["category_test"] = {
                "total_categories": len(categories),
                "categories": categories,
                "status": "success"
            }
            
            return categories
            
        except Exception as e:
            error_msg = f"分类识别测试失败: {str(e)}"
            logger.error(error_msg)
            self.test_results["category_test"] = {
                "status": "failed",
                "error": str(e)
            }
            return {}
            
    async def test_product_extraction_quality(self, categories):
        """测试产品数据提取质量"""
        logger.info("=" * 60)
        logger.info("测试2: 产品数据提取质量测试")
        logger.info("=" * 60)
        
        if not categories:
            logger.error("没有可用的分类，跳过产品提取测试")
            return []
            
        all_products = []
        extraction_results = {}
        
        # 测试前3个分类
        test_categories = list(categories.items())[:3]
        
        for category_name, category_url in test_categories:
            try:
                logger.info(f"测试分类: {category_name}")
                
                # 爬取该分类的产品（限制数量为5个）
                products = await self.scraper.scrape_category_products(category_name, category_url)
                
                # 限制产品数量以加快测试
                products = products[:5]
                all_products.extend(products)
                
                logger.info(f"  从 '{category_name}' 提取到 {len(products)} 个产品")
                
                # 分析数据质量
                quality_analysis = self.analyze_extraction_quality(products)
                
                extraction_results[category_name] = {
                    "product_count": len(products),
                    "sample_products": products[:2],  # 保存前2个作为样本
                    "quality": quality_analysis
                }
                
                # 显示样本产品信息
                if products:
                    sample = products[0]
                    logger.info(f"  样本产品名称: {sample.get('name', '未知')}")
                    logger.info(f"  技术参数数量: {len(sample.get('technical_specs', {}))}")
                    logger.info(f"  特征数量: {len(sample.get('features', []))}")
                    
            except Exception as e:
                error_msg = f"分类 '{category_name}' 产品提取失败: {str(e)}"
                logger.error(error_msg)
                extraction_results[category_name] = {
                    "status": "failed",
                    "error": str(e)
                }
                
        self.test_results["product_extraction_test"] = extraction_results
        return all_products
        
    def analyze_extraction_quality(self, products):
        """分析提取质量"""
        if not products:
            return {"status": "no_products"}
            
        total = len(products)
        quality_metrics = {
            "has_clean_name": 0,
            "has_description": 0,
            "has_price": 0,
            "has_technical_specs": 0,
            "has_valid_features": 0,
            "has_images": 0
        }
        
        for product in products:
            # 检查产品名称是否已清理
            name = product.get('name', '')
            if name and not any(suffix in name for suffix in ['至璨/ZHICAN', '浙江兆众']):
                quality_metrics["has_clean_name"] += 1
                
            if product.get('description'):
                quality_metrics["has_description"] += 1
            if product.get('price'):
                quality_metrics["has_price"] += 1
            if product.get('technical_specs'):
                quality_metrics["has_technical_specs"] += 1
            if product.get('images'):
                quality_metrics["has_images"] += 1
                
            # 检查特征是否有效（不包含导航信息）
            features = product.get('features', [])
            valid_features = [f for f in features if not any(
                keyword in f for keyword in ['关于我们', '联系我们', '产品中心', '0571-88640980']
            )]
            if valid_features:
                quality_metrics["has_valid_features"] += 1
                
        # 计算百分比
        quality_percentages = {}
        for key, count in quality_metrics.items():
            quality_percentages[key] = round((count / total) * 100, 2)
            
        return {
            "total_products": total,
            "metrics": quality_metrics,
            "percentages": quality_percentages
        }
        
    async def test_document_generation(self, products_data):
        """测试文档生成功能"""
        logger.info("=" * 60)
        logger.info("测试3: 销售文档生成测试")
        logger.info("=" * 60)
        
        if not products_data:
            logger.error("没有产品数据，跳过文档生成测试")
            return False
            
        try:
            # 数据处理
            processor = DataProcessor()
            clean_data = processor.clean_and_structure_data(products_data)
            categories = processor.categorize_products(clean_data)
            tech_specs = processor.extract_technical_specifications(clean_data)
            
            logger.info(f"数据处理完成:")
            logger.info(f"  - 清洗后产品数: {len(clean_data)}")
            logger.info(f"  - 分类数: {len(categories)}")
            
            # 创建输出目录
            output_dir = Path("improved_crawler_output")
            output_dir.mkdir(exist_ok=True)
            
            # 文档生成
            generator = DocumentGenerator(clean_data, categories, tech_specs)
            await generator.generate_all_documents(output_dir)
            
            # 检查生成的文件
            generated_files = list(output_dir.glob("*"))
            logger.info(f"✓ 成功生成 {len(generated_files)} 个销售工具文件:")
            
            for file in generated_files:
                file_size = file.stat().st_size
                logger.info(f"  - {file.name} ({file_size} bytes)")
                
            self.test_results["document_generation_test"] = {
                "status": "success",
                "files_generated": len(generated_files),
                "output_directory": str(output_dir)
            }
            
            return True
            
        except Exception as e:
            error_msg = f"文档生成测试失败: {str(e)}"
            logger.error(error_msg)
            self.test_results["document_generation_test"] = {
                "status": "failed",
                "error": str(e)
            }
            return False
            
    async def generate_test_report(self):
        """生成测试报告"""
        logger.info("=" * 60)
        logger.info("生成改进后爬虫测试报告")
        logger.info("=" * 60)
        
        # 创建报告目录
        report_dir = Path("improved_crawler_reports")
        report_dir.mkdir(exist_ok=True)
        
        # 保存详细测试结果
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        results_file = report_dir / f"improved_crawler_test_{timestamp}.json"
        
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, ensure_ascii=False, indent=2)
            
        logger.info(f"测试报告已保存: {results_file}")
        
        # 生成总结
        self.generate_summary()
        
    def generate_summary(self):
        """生成测试总结"""
        logger.info("=" * 60)
        logger.info("🎯 改进后爬虫测试总结")
        logger.info("=" * 60)
        
        category_test = self.test_results.get("category_test", {})
        if category_test.get("status") == "success":
            logger.info(f"✅ 分类识别: 成功识别 {category_test.get('total_categories', 0)} 个分类")
        else:
            logger.info("❌ 分类识别: 失败")
            
        extraction_test = self.test_results.get("product_extraction_test", {})
        successful_categories = sum(1 for result in extraction_test.values() 
                                  if isinstance(result, dict) and result.get("product_count", 0) > 0)
        logger.info(f"✅ 产品提取: {successful_categories} 个分类成功提取产品")
        
        doc_test = self.test_results.get("document_generation_test", {})
        if doc_test.get("status") == "success":
            logger.info(f"✅ 文档生成: 成功生成 {doc_test.get('files_generated', 0)} 个销售工具文件")
        else:
            logger.info("❌ 文档生成: 失败")

async def main():
    """主测试函数"""
    logger.info("🚀 开始改进后的爬虫系统完整测试...")
    
    tester = ImprovedCrawlerTester()
    
    try:
        await tester.initialize()
        
        # 执行测试序列
        categories = await tester.test_category_recognition()
        products = await tester.test_product_extraction_quality(categories)
        await tester.test_document_generation(products)
        
        # 生成测试报告
        await tester.generate_test_report()
        
        logger.info("🎉 改进后爬虫系统测试完成！")
        
    except Exception as e:
        logger.error(f"测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        
    finally:
        await tester.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
