#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
<AUTHOR> 
@license          http://www.178188.xyz
@lastmodify       2025年8月1日
模块说明: 完整系统运行脚本 - 爬取数据并生成销售文档
"""

import asyncio
import logging
import json
from pathlib import Path
import sys
from datetime import datetime

# 添加src目录到路径
sys.path.append(str(Path(__file__).parent / "src"))

from zhaozhong_scraper_v2 import ZhaoZhongAIScraperV2
from document_generator import SalesDocumentGenerator

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('complete_system.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

async def run_complete_system():
    """运行完整的爬虫和文档生成系统"""
    logger.info("=" * 60)
    logger.info("开始运行完整的销售人员产品匹配系统")
    logger.info("=" * 60)
    
    # 创建输出目录
    output_dir = Path("output") / datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir.mkdir(parents=True, exist_ok=True)
    logger.info(f"输出目录: {output_dir}")
    
    products_data = []
    
    try:
        # 第一步：爬取产品数据
        logger.info("\n" + "=" * 40)
        logger.info("第一步：爬取产品数据")
        logger.info("=" * 40)
        
        scraper = ZhaoZhongAIScraperV2()
        await scraper.initialize()
        
        logger.info("开始爬取赵中AI网站产品数据...")
        products_data = await scraper.scrape_all_products()
        
        logger.info(f"爬取完成！共获取 {len(products_data)} 个产品")
        
        # 保存原始数据
        raw_data_file = output_dir / "raw_products_data.json"
        with open(raw_data_file, 'w', encoding='utf-8') as f:
            json.dump(products_data, f, ensure_ascii=False, indent=2)
        logger.info(f"原始数据已保存: {raw_data_file}")
        
        await scraper.close()
        
        # 第二步：生成销售文档
        logger.info("\n" + "=" * 40)
        logger.info("第二步：生成销售支持文档")
        logger.info("=" * 40)
        
        if not products_data:
            logger.error("没有产品数据，无法生成文档")
            return
            
        # 初始化文档生成器
        doc_generator = SalesDocumentGenerator(products_data)
        
        # 显示统计信息
        stats = doc_generator.get_statistics()
        logger.info("产品数据统计:")
        for key, value in stats.items():
            logger.info(f"  {key}: {value}")
        
        # 生成所有文档
        await doc_generator.generate_all_documents(output_dir)
        
        # 保存处理后的数据
        doc_generator.save_products_json(output_dir)
        
        # 第三步：生成总结报告
        logger.info("\n" + "=" * 40)
        logger.info("第三步：生成总结报告")
        logger.info("=" * 40)
        
        await generate_summary_report(output_dir, stats, products_data)
        
        logger.info("\n" + "=" * 60)
        logger.info("系统运行完成！")
        logger.info(f"所有文件已保存到: {output_dir}")
        logger.info("=" * 60)
        
        # 显示生成的文件列表
        logger.info("\n生成的文件:")
        for file_path in sorted(output_dir.rglob("*")):
            if file_path.is_file():
                logger.info(f"  - {file_path.name}")
        
        return output_dir
        
    except Exception as e:
        logger.error(f"系统运行出错: {str(e)}")
        raise
        
async def generate_summary_report(output_dir: Path, stats: dict, products_data: list):
    """生成总结报告"""
    logger.info("生成系统运行总结报告...")
    
    report_content = []
    report_content.append("# 销售人员产品匹配系统运行报告\n")
    report_content.append(f"生成时间: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}\n")
    report_content.append("---\n")
    
    report_content.append("## 数据爬取结果\n")
    report_content.append(f"- 总产品数量: {stats['total_products']}\n")
    report_content.append(f"- 产品分类数: {stats['categories']}\n")
    report_content.append(f"- 子分类数: {stats['total_subcategories']}\n")
    report_content.append(f"- 包含PDF资料的产品: {stats['products_with_pdf']}\n")
    report_content.append(f"- 包含技术规格的产品: {stats['products_with_specs']}\n")
    report_content.append(f"- 包含应用场景的产品: {stats['products_with_applications']}\n")
    report_content.append(f"- 包含产品特点的产品: {stats['products_with_features']}\n")
    
    report_content.append("## 数据质量分析\n")
    report_content.append(f"- 平均每个产品的技术规格数: {stats['avg_specs_per_product']:.1f}\n")
    report_content.append(f"- 平均每个产品的特点数: {stats['avg_features_per_product']:.1f}\n")
    report_content.append(f"- 总应用场景数: {stats['total_applications']}\n")
    
    # 数据完整性评分
    completeness_score = (
        (stats['products_with_pdf'] / stats['total_products']) * 0.3 +
        (stats['products_with_specs'] / stats['total_products']) * 0.4 +
        (stats['products_with_applications'] / stats['total_products']) * 0.2 +
        (stats['products_with_features'] / stats['total_products']) * 0.1
    ) * 100
    
    report_content.append(f"- 数据完整性评分: {completeness_score:.1f}%\n")
    
    report_content.append("## 生成的销售支持文档\n")
    report_content.append("1. **完整产品目录.md** - 包含所有产品的详细信息\n")
    report_content.append("2. **销售人员产品推荐指南.md** - 按应用场景分类的推荐指南\n")
    report_content.append("3. **行业特定推荐指南** - 针对不同行业的专门指南\n")
    report_content.append("4. **产品对比表.xlsx** - Excel格式的产品对比表\n")
    report_content.append("5. **客户需求快速匹配指南.md** - 客户需求分析和匹配方法\n")
    report_content.append("6. **专业销售话术库.md** - 基于实际产品的销售话术\n")
    report_content.append("7. **products_data.json** - 结构化的产品数据\n")
    
    report_content.append("## 使用建议\n")
    report_content.append("### 销售人员使用指南\n")
    report_content.append("1. **日常使用**: 优先查看《销售人员产品推荐指南》\n")
    report_content.append("2. **客户沟通**: 参考《客户需求快速匹配指南》进行需求分析\n")
    report_content.append("3. **产品介绍**: 使用《专业销售话术库》中的标准话术\n")
    report_content.append("4. **产品对比**: 使用Excel对比表进行产品比较\n")
    report_content.append("5. **行业客户**: 使用对应的行业特定指南\n")
    
    report_content.append("### 系统维护建议\n")
    report_content.append("1. **定期更新**: 建议每月运行一次系统更新产品数据\n")
    report_content.append("2. **数据验证**: 定期检查PDF链接的有效性\n")
    report_content.append("3. **内容优化**: 根据销售反馈优化话术和推荐逻辑\n")
    
    if completeness_score < 80:
        report_content.append("## 数据质量改进建议\n")
        report_content.append("- 当前数据完整性较低，建议:\n")
        report_content.append("  1. 检查PDF下载和解析逻辑\n")
        report_content.append("  2. 优化文本提取算法\n")
        report_content.append("  3. 增加人工数据补充\n")
    
    # 保存报告
    report_file = output_dir / "系统运行报告.md"
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write('\n'.join(report_content))
        
    logger.info(f"系统运行报告已保存: {report_file}")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='运行完整的销售人员产品匹配系统')
    parser.add_argument('--test-mode', action='store_true', 
                       help='测试模式，只爬取少量数据')
    
    args = parser.parse_args()
    
    if args.test_mode:
        logger.info("运行在测试模式")
        # 可以在这里添加测试模式的特殊逻辑
    
    try:
        output_dir = asyncio.run(run_complete_system())
        print(f"\n✅ 系统运行成功！")
        print(f"📁 输出目录: {output_dir}")
        print(f"📊 请查看 '系统运行报告.md' 了解详细结果")
        
    except KeyboardInterrupt:
        logger.info("用户中断了程序运行")
        print("\n⚠️  程序被用户中断")
        
    except Exception as e:
        logger.error(f"程序运行失败: {str(e)}")
        print(f"\n❌ 程序运行失败: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
