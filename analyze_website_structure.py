#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
<AUTHOR> 
@license          http://www.178188.xyz
@lastmodify       2025年8月1日
模块说明: 赵中AI网站结构深度分析工具
"""

import asyncio
import logging
import json
from pathlib import Path
from datetime import datetime
from playwright.async_api import async_playwright
from urllib.parse import urljoin, urlparse
import re

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class WebsiteStructureAnalyzer:
    """网站结构分析器"""
    
    def __init__(self):
        self.base_url = "https://www.zhaozhongai.com"
        self.browser = None
        self.page = None
        self.analysis_results = {
            "pages_analyzed": {},
            "navigation_structure": {},
            "product_categories": {},
            "product_samples": {},
            "html_patterns": {},
            "recommendations": []
        }
        
    async def initialize(self):
        """初始化浏览器"""
        logger.info("初始化网站结构分析器...")
        self.playwright = await async_playwright().start()
        self.browser = await self.playwright.chromium.launch(
            headless=False,  # 设为False以便观察页面
            args=['--no-sandbox', '--disable-dev-shm-usage']
        )
        self.page = await self.browser.new_page()
        
        # 设置用户代理
        await self.page.set_extra_http_headers({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        })
        
    async def cleanup(self):
        """清理资源"""
        if self.browser:
            await self.browser.close()
        if hasattr(self, 'playwright'):
            await self.playwright.stop()
            
    async def analyze_page_structure(self, url, page_name):
        """分析单个页面的结构"""
        logger.info(f"分析页面: {page_name} - {url}")
        
        try:
            await self.page.goto(url, wait_until='networkidle', timeout=30000)
            await asyncio.sleep(2)  # 等待页面完全加载
            
            # 获取页面基本信息
            title = await self.page.title()
            logger.info(f"页面标题: {title}")
            
            # 分析页面结构
            structure_info = {
                "url": url,
                "title": title,
                "navigation": await self.analyze_navigation(),
                "main_content": await self.analyze_main_content(),
                "product_links": await self.find_all_links_detailed(),
                "forms": await self.analyze_forms(),
                "images": await self.analyze_images(),
                "scripts_and_styles": await self.analyze_scripts_styles()
            }
            
            self.analysis_results["pages_analyzed"][page_name] = structure_info
            
            # 保存页面截图
            screenshot_dir = Path("website_analysis/screenshots")
            screenshot_dir.mkdir(parents=True, exist_ok=True)
            await self.page.screenshot(path=screenshot_dir / f"{page_name}.png")
            
            # 保存页面HTML
            html_dir = Path("website_analysis/html")
            html_dir.mkdir(parents=True, exist_ok=True)
            html_content = await self.page.content()
            with open(html_dir / f"{page_name}.html", 'w', encoding='utf-8') as f:
                f.write(html_content)
                
            logger.info(f"✓ {page_name} 分析完成")
            return structure_info
            
        except Exception as e:
            logger.error(f"分析页面 {page_name} 失败: {str(e)}")
            return None
            
    async def analyze_navigation(self):
        """分析导航结构"""
        nav_info = {
            "main_nav": [],
            "sub_nav": [],
            "breadcrumbs": [],
            "footer_nav": []
        }
        
        # 主导航
        nav_selectors = [
            'nav', '.nav', '.navigation', '.menu', '.main-menu',
            'header nav', '.header-nav', '.top-nav'
        ]
        
        for selector in nav_selectors:
            try:
                nav_elements = await self.page.query_selector_all(f"{selector} a")
                for element in nav_elements:
                    href = await element.get_attribute('href')
                    text = await element.inner_text()
                    if href and text and text.strip():
                        nav_info["main_nav"].append({
                            "text": text.strip(),
                            "href": href,
                            "full_url": urljoin(self.base_url, href)
                        })
                if nav_info["main_nav"]:
                    break
            except:
                continue
                
        # 面包屑导航
        breadcrumb_selectors = [
            '.breadcrumb', '.breadcrumbs', '.crumb', '.path'
        ]
        
        for selector in breadcrumb_selectors:
            try:
                breadcrumb_elements = await self.page.query_selector_all(f"{selector} a")
                for element in breadcrumb_elements:
                    href = await element.get_attribute('href')
                    text = await element.inner_text()
                    if text and text.strip():
                        nav_info["breadcrumbs"].append({
                            "text": text.strip(),
                            "href": href
                        })
                if nav_info["breadcrumbs"]:
                    break
            except:
                continue
                
        return nav_info
        
    async def analyze_main_content(self):
        """分析主要内容区域"""
        content_info = {
            "headings": [],
            "paragraphs": [],
            "lists": [],
            "tables": [],
            "content_blocks": []
        }
        
        # 标题
        for level in range(1, 7):
            headings = await self.page.query_selector_all(f"h{level}")
            for heading in headings:
                text = await heading.inner_text()
                if text and text.strip():
                    content_info["headings"].append({
                        "level": level,
                        "text": text.strip()
                    })
                    
        # 段落
        paragraphs = await self.page.query_selector_all("p")
        for p in paragraphs[:10]:  # 限制数量
            text = await p.inner_text()
            if text and len(text.strip()) > 10:
                content_info["paragraphs"].append(text.strip()[:200])
                
        # 列表
        lists = await self.page.query_selector_all("ul, ol")
        for ul in lists[:5]:  # 限制数量
            items = await ul.query_selector_all("li")
            list_items = []
            for item in items[:10]:  # 每个列表最多10项
                text = await item.inner_text()
                if text and text.strip():
                    list_items.append(text.strip())
            if list_items:
                content_info["lists"].append(list_items)
                
        # 表格
        tables = await self.page.query_selector_all("table")
        for table in tables[:3]:  # 限制数量
            rows = await table.query_selector_all("tr")
            table_data = []
            for row in rows[:5]:  # 每个表格最多5行
                cells = await row.query_selector_all("td, th")
                row_data = []
                for cell in cells:
                    text = await cell.inner_text()
                    row_data.append(text.strip() if text else "")
                if row_data:
                    table_data.append(row_data)
            if table_data:
                content_info["tables"].append(table_data)
                
        return content_info
        
    async def find_all_links_detailed(self):
        """详细分析所有链接"""
        links_info = {
            "internal_links": [],
            "external_links": [],
            "product_links": [],
            "category_links": [],
            "download_links": []
        }
        
        all_links = await self.page.query_selector_all("a[href]")
        
        for link in all_links:
            href = await link.get_attribute('href')
            text = await link.inner_text()
            title = await link.get_attribute('title')
            class_name = await link.get_attribute('class')
            
            if not href:
                continue
                
            full_url = urljoin(self.base_url, href)
            parsed_url = urlparse(full_url)
            
            link_data = {
                "text": text.strip() if text else "",
                "href": href,
                "full_url": full_url,
                "title": title if title else "",
                "class": class_name if class_name else ""
            }
            
            # 分类链接
            if parsed_url.netloc == urlparse(self.base_url).netloc:
                links_info["internal_links"].append(link_data)
                
                # 判断是否为产品链接
                if any(keyword in href.lower() for keyword in ['product', 'item', 'detail']) or \
                   any(keyword in text.lower() for keyword in ['传感器', '模块', '开关', '继电器']):
                    links_info["product_links"].append(link_data)
                    
                # 判断是否为分类链接
                if any(keyword in text for keyword in ['至璨', 'CPU', 'IO', '传感器', '继电器']):
                    links_info["category_links"].append(link_data)
                    
                # 判断是否为下载链接
                if any(keyword in href.lower() for keyword in ['download', 'file', 'pdf']) or \
                   any(keyword in text.lower() for keyword in ['下载', '资料', '手册']):
                    links_info["download_links"].append(link_data)
            else:
                links_info["external_links"].append(link_data)
                
        return links_info
        
    async def analyze_forms(self):
        """分析表单"""
        forms_info = []
        
        forms = await self.page.query_selector_all("form")
        for form in forms:
            action = await form.get_attribute('action')
            method = await form.get_attribute('method')
            
            inputs = await form.query_selector_all("input, select, textarea")
            form_fields = []
            
            for input_elem in inputs:
                input_type = await input_elem.get_attribute('type')
                name = await input_elem.get_attribute('name')
                placeholder = await input_elem.get_attribute('placeholder')
                
                form_fields.append({
                    "type": input_type,
                    "name": name,
                    "placeholder": placeholder
                })
                
            forms_info.append({
                "action": action,
                "method": method,
                "fields": form_fields
            })
            
        return forms_info
        
    async def analyze_images(self):
        """分析图片"""
        images_info = []
        
        images = await self.page.query_selector_all("img")
        for img in images[:20]:  # 限制数量
            src = await img.get_attribute('src')
            alt = await img.get_attribute('alt')
            title = await img.get_attribute('title')
            
            if src:
                images_info.append({
                    "src": src,
                    "full_url": urljoin(self.base_url, src),
                    "alt": alt if alt else "",
                    "title": title if title else ""
                })
                
        return images_info
        
    async def analyze_scripts_styles(self):
        """分析脚本和样式"""
        scripts_styles = {
            "scripts": [],
            "stylesheets": []
        }
        
        # 脚本
        scripts = await self.page.query_selector_all("script[src]")
        for script in scripts:
            src = await script.get_attribute('src')
            if src:
                scripts_styles["scripts"].append(urljoin(self.base_url, src))
                
        # 样式表
        stylesheets = await self.page.query_selector_all("link[rel='stylesheet']")
        for stylesheet in stylesheets:
            href = await stylesheet.get_attribute('href')
            if href:
                scripts_styles["stylesheets"].append(urljoin(self.base_url, href))
                
        return scripts_styles

async def main():
    """主分析函数"""
    logger.info("开始赵中AI网站结构深度分析...")
    
    analyzer = WebsiteStructureAnalyzer()
    
    try:
        await analyzer.initialize()
        
        # 要分析的页面列表
        pages_to_analyze = [
            ("主页", "https://www.zhaozhongai.com/"),
            ("产品中心", "https://www.zhaozhongai.com/product/"),
            ("下载中心", "https://www.zhaozhongai.com/download/")
        ]
        
        # 逐页分析
        for page_name, url in pages_to_analyze:
            await analyzer.analyze_page_structure(url, page_name)
            await asyncio.sleep(3)  # 页面间延迟
            
        # 保存分析结果
        analysis_dir = Path("website_analysis")
        analysis_dir.mkdir(exist_ok=True)
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        results_file = analysis_dir / f"structure_analysis_{timestamp}.json"
        
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(analyzer.analysis_results, f, ensure_ascii=False, indent=2)
            
        logger.info(f"网站结构分析完成！结果保存在: {results_file}")
        logger.info("请查看 website_analysis/ 目录中的截图和HTML文件")
        
    except Exception as e:
        logger.error(f"分析过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        
    finally:
        await analyzer.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
