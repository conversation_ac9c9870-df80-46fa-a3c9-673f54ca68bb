#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
<AUTHOR> 
@license          http://www.178188.xyz
@lastmodify       2025年8月1日
模块说明: 赵中AI网站爬虫V2 - 基于资料下载页面的正确实现
"""

import asyncio
import logging
import re
import json
from typing import List, Dict, Any, Optional
from urllib.parse import urljoin, urlparse
from playwright.async_api import async_playwright, <PERSON>, Browser
from pathlib import Path
import PyPDF2
import requests
from io import BytesIO

logger = logging.getLogger(__name__)

class ZhaoZhongAIScraperV2:
    """赵中AI网站爬虫V2 - 正确版本"""
    
    def __init__(self):
        self.base_url = "https://www.zhaozhongai.com"
        self.download_url = "https://www.zhaozhongai.com/down/"
        self.browser: Optional[Browser] = None
        self.page: Optional[Page] = None
        self.products_data = []
        self.pdf_cache = {}  # PDF内容缓存
        
    async def initialize(self):
        """初始化浏览器"""
        logger.info("初始化浏览器...")
        self.playwright = await async_playwright().start()
        self.browser = await self.playwright.chromium.launch(
            headless=True,
            args=['--no-sandbox', '--disable-dev-shm-usage']
        )
        self.page = await self.browser.new_page()
        
        # 设置用户代理
        await self.page.set_extra_http_headers({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        })
        
    async def close(self):
        """关闭浏览器"""
        if self.browser:
            await self.browser.close()
        if hasattr(self, 'playwright'):
            await self.playwright.stop()
            
    async def scrape_all_products(self) -> List[Dict[str, Any]]:
        """爬取所有产品信息"""
        try:
            logger.info(f"访问产品中心页面: {self.base_url}/product/")
            await self.page.goto(f"{self.base_url}/product/", wait_until='networkidle')

            # 获取所有分类页面链接
            category_links = await self.get_category_links()
            logger.info(f"发现 {len(category_links)} 个产品分类页面")

            all_products = []

            for category_info in category_links:
                category_name = category_info['name']
                category_url = category_info['url']

                logger.info(f"处理分类: {category_name} - {category_url}")

                try:
                    # 访问分类页面
                    await self.page.goto(category_url, wait_until='networkidle')
                    await asyncio.sleep(3)

                    # 获取该分类下的产品
                    products = await self.get_products_in_category_page(category_name, category_url)
                    logger.info(f"  在 {category_name} 中找到 {len(products)} 个产品")

                    for product in products:
                        # 下载并解析PDF
                        if product.get('pdf_url'):
                            pdf_content = await self.download_and_parse_pdf(product['pdf_url'])
                            if pdf_content:
                                product['pdf_content'] = pdf_content
                                product['technical_specs'] = self.extract_specs_from_pdf(pdf_content)
                                product['applications'] = self.extract_applications_from_pdf(pdf_content)
                                product['features'] = self.extract_features_from_pdf(pdf_content)

                        all_products.append(product)

                    # 添加延迟避免被反爬
                    await asyncio.sleep(2)

                except Exception as e:
                    logger.error(f"处理分类 {category_name} 时出错: {str(e)}")
                    continue

            logger.info(f"总共爬取到 {len(all_products)} 个产品")
            return all_products

        except Exception as e:
            logger.error(f"爬取过程出错: {str(e)}")
            raise

    async def get_category_links(self) -> List[Dict[str, str]]:
        """获取所有分类页面链接"""
        category_links = []

        try:
            # 基于HTML分析，直接构造已知的分类链接
            known_categories = [
                {'name': '至璨 · CPU单元', 'path': '/productlist1/'},
                {'name': '至璨 · 总线耦合器', 'path': '/productlist2/'},
                {'name': '至璨 · IO模块', 'path': '/mokuai/'},
                {'name': '至璨 · 接近传感器', 'path': '/jiejinchuanganqi/'},
                {'name': '至璨 · 光电传感器', 'path': '/guangdianchuanganqi/'},
                {'name': '至璨 · 继电器', 'path': '/-chong--lu-juan----hong--hu-dian-/'},
                {'name': '至璨 · 激光光电传感器', 'path': '/jiguangguangdianchuanganqi/'},
                {'name': '至璨 · 激光位移传感器', 'path': '/jiguangweiyichuanganqi/'},
                {'name': '至璨 · 色标传感器', 'path': '/sebiaochuanganqi/'},
                {'name': '至璨 · 光纤传感器', 'path': '/guangxianchuanganqi/'},
                {'name': '至璨 · 微动/行程/限位开关', 'path': '/-chong--lu-huan--liao---/'},
                {'name': '至璨 · 管道液位传感器', 'path': '/-chong--lu-bing--liao-donghuan-----/'}
            ]

            for category in known_categories:
                category_links.append({
                    'name': category['name'],
                    'url': urljoin(self.base_url, category['path'])
                })

            # 也尝试从页面动态获取链接作为补充
            try:
                link_elements = await self.page.query_selector_all('a[href*="/productlist"], a[href*="/mokuai"], a[href*="chuanganqi"], a[href*="chong"]')

                for element in link_elements:
                    href = await element.get_attribute('href')
                    text = await element.inner_text()

                    if href and text and "至璨" in text:
                        full_url = urljoin(self.base_url, href)

                        # 检查是否已存在
                        if not any(link['url'] == full_url for link in category_links):
                            category_links.append({
                                'name': text.strip(),
                                'url': full_url
                            })

            except Exception as e:
                logger.debug(f"动态获取链接失败: {e}")

        except Exception as e:
            logger.error(f"获取分类链接失败: {str(e)}")

        return category_links

    async def get_products_in_category_page(self, category_name: str, category_url: str) -> List[Dict[str, Any]]:
        """获取分类页面中的产品"""
        products = []

        try:
            # 等待页面加载
            await asyncio.sleep(3)

            # 查找产品详情页面链接
            product_links = await self.find_product_detail_links()
            logger.info(f"在 {category_name} 页面找到 {len(product_links)} 个产品详情链接")

            for product_link in product_links:
                try:
                    product_info = await self.scrape_product_detail_page(
                        product_link, category_name, category_url
                    )

                    if product_info:
                        products.append(product_info)
                        logger.info(f"成功获取产品: {product_info['name']}")

                except Exception as e:
                    logger.error(f"处理产品详情页面时出错: {e}")
                    continue

            # 如果没有找到产品详情链接，尝试查找直接的PDF链接
            if not products:
                logger.info(f"在 {category_name} 中没有找到产品详情链接，尝试查找直接PDF链接")
                products = await self.find_direct_pdf_links(category_name, category_url)

        except Exception as e:
            logger.error(f"获取分类页面产品失败: {str(e)}")

        return products

    async def find_product_detail_links(self) -> List[Dict[str, str]]:
        """查找产品详情页面链接"""
        product_links = []

        try:
            # 查找产品容器
            product_containers = await self.page.query_selector_all('[id*="product"]')
            logger.debug(f"找到 {len(product_containers)} 个产品容器")

            for container in product_containers:
                try:
                    # 查找产品链接
                    link_element = await container.query_selector('a[href*="/product/"]')
                    if link_element:
                        href = await link_element.get_attribute('href')

                        # 获取产品名称
                        name_element = await container.query_selector('.en a, .title, h3, h4')
                        product_name = ""
                        if name_element:
                            product_name = await name_element.inner_text()

                        # 获取产品图片
                        img_element = await container.query_selector('img')
                        image_url = ""
                        if img_element:
                            src = await img_element.get_attribute('src')
                            if src:
                                image_url = urljoin(self.base_url, src)

                        if href and product_name:
                            product_links.append({
                                'name': product_name.strip(),
                                'url': urljoin(self.base_url, href),
                                'image_url': image_url
                            })

                except Exception as e:
                    logger.debug(f"处理产品容器时出错: {e}")
                    continue

            # 如果没有找到，尝试其他选择器
            if not product_links:
                logger.debug("尝试其他产品链接选择器...")

                # 直接查找产品页面链接
                all_product_links = await self.page.query_selector_all('a[href*="/product/"]')

                for link in all_product_links:
                    try:
                        href = await link.get_attribute('href')
                        text = await link.inner_text()

                        if href and text and text.strip():
                            product_links.append({
                                'name': text.strip(),
                                'url': urljoin(self.base_url, href),
                                'image_url': ''
                            })

                    except Exception as e:
                        logger.debug(f"处理产品链接时出错: {e}")
                        continue

        except Exception as e:
            logger.error(f"查找产品详情链接失败: {str(e)}")

        return product_links

    async def scrape_product_detail_page(self, product_link: Dict[str, str], category_name: str, category_url: str) -> Optional[Dict[str, Any]]:
        """爬取产品详情页面"""
        try:
            product_url = product_link['url']
            logger.debug(f"访问产品详情页面: {product_url}")

            # 访问产品详情页面
            await self.page.goto(product_url, wait_until='networkidle')
            await asyncio.sleep(2)

            # 查找PDF下载链接
            pdf_url = await self.find_pdf_download_link()

            # 获取产品描述
            description = await self.extract_product_description()

            # 获取产品规格信息
            specs_text = await self.extract_product_specs_from_page()

            product_info = {
                'name': product_link['name'],
                'category': category_name,
                'subcategory': category_name,
                'description': description,
                'pdf_url': pdf_url,
                'image_url': product_link.get('image_url', ''),
                'scraped_at': asyncio.get_event_loop().time(),
                'source_url': category_url,
                'detail_url': product_url,
                'page_specs': specs_text  # 页面中的规格信息
            }

            return product_info

        except Exception as e:
            logger.error(f"爬取产品详情页面失败: {str(e)}")
            return None

    async def find_pdf_download_link(self) -> str:
        """在产品详情页面查找PDF下载链接"""
        try:
            # 策略1：查找直接的PDF链接
            pdf_links = await self.page.query_selector_all('a[href*=".pdf"]')
            if pdf_links:
                href = await pdf_links[0].get_attribute('href')
                if href:
                    return urljoin(self.base_url, href)

            # 策略2：查找"资料下载"或"下载"按钮
            download_selectors = [
                'a:has-text("资料下载")',
                'a:has-text("下载")',
                'a:has-text("立即下载")',
                '.download',
                '.btn-download'
            ]

            for selector in download_selectors:
                try:
                    download_element = await self.page.query_selector(selector)
                    if download_element:
                        # 点击下载按钮
                        await download_element.click()
                        await asyncio.sleep(2)

                        # 检查是否出现了PDF链接
                        new_pdf_links = await self.page.query_selector_all('a[href*=".pdf"]')
                        if new_pdf_links:
                            href = await new_pdf_links[0].get_attribute('href')
                            if href:
                                return urljoin(self.base_url, href)

                except Exception as e:
                    logger.debug(f"尝试下载选择器 {selector} 失败: {e}")
                    continue

            # 策略3：检查是否需要跳转到下载页面
            download_page_link = await self.page.query_selector('a[href*="/download"]')
            if download_page_link:
                href = await download_page_link.get_attribute('href')
                if href:
                    download_url = urljoin(self.base_url, href)
                    logger.debug(f"尝试访问下载页面: {download_url}")

                    # 访问下载页面
                    await self.page.goto(download_url, wait_until='networkidle')
                    await asyncio.sleep(2)

                    # 在下载页面查找PDF链接
                    download_page_pdfs = await self.page.query_selector_all('a[href*=".pdf"]')
                    if download_page_pdfs:
                        href = await download_page_pdfs[0].get_attribute('href')
                        if href:
                            return urljoin(self.base_url, href)

        except Exception as e:
            logger.debug(f"查找PDF下载链接失败: {e}")

        return ""

    async def extract_product_description(self) -> str:
        """提取产品描述"""
        try:
            # 查找描述元素
            desc_selectors = [
                '.content p',
                '.description',
                '.desc',
                '.intro',
                '.summary'
            ]

            for selector in desc_selectors:
                try:
                    desc_element = await self.page.query_selector(selector)
                    if desc_element:
                        desc_text = await desc_element.inner_text()
                        if desc_text and desc_text.strip():
                            return desc_text.strip()
                except:
                    continue

        except Exception as e:
            logger.debug(f"提取产品描述失败: {e}")

        return ""

    async def extract_product_specs_from_page(self) -> str:
        """从页面提取产品规格信息"""
        try:
            # 查找规格表格或列表
            specs_selectors = [
                '.specs',
                '.specifications',
                '.parameters',
                '.tech-specs',
                'table',
                '.spec-table'
            ]

            for selector in specs_selectors:
                try:
                    specs_element = await self.page.query_selector(selector)
                    if specs_element:
                        specs_text = await specs_element.inner_text()
                        if specs_text and specs_text.strip():
                            return specs_text.strip()
                except:
                    continue

        except Exception as e:
            logger.debug(f"提取产品规格失败: {e}")

        return ""

    async def find_direct_pdf_links(self, category_name: str, category_url: str) -> List[Dict[str, Any]]:
        """查找直接的PDF链接（备用方法）"""
        products = []

        try:
            # 查找PDF下载链接
            pdf_links = await self.page.query_selector_all('a[href*=".pdf"]')
            logger.info(f"在 {category_name} 页面找到 {len(pdf_links)} 个直接PDF链接")

            for pdf_link in pdf_links:
                try:
                    # 获取PDF链接
                    href = await pdf_link.get_attribute('href')
                    if not href:
                        continue

                    pdf_url = urljoin(self.base_url, href)

                    # 获取产品名称 - 多种策略
                    product_name = await self.extract_product_name_from_pdf_link(pdf_link)

                    if product_name and pdf_url:
                        product_info = {
                            'name': product_name,
                            'category': category_name,
                            'subcategory': category_name,
                            'description': '',
                            'pdf_url': pdf_url,
                            'image_url': '',
                            'scraped_at': asyncio.get_event_loop().time(),
                            'source_url': category_url
                        }

                        products.append(product_info)
                        logger.debug(f"找到产品: {product_name} - {pdf_url}")

                except Exception as e:
                    logger.debug(f"处理PDF链接时出错: {e}")
                    continue

        except Exception as e:
            logger.error(f"查找直接PDF链接失败: {str(e)}")

        return products

    async def extract_product_name_from_pdf_link(self, pdf_link_element) -> str:
        """从PDF链接元素提取产品名称"""
        try:
            # 策略1：获取链接文本
            link_text = await pdf_link_element.inner_text()
            if link_text and link_text.strip() and "下载" not in link_text:
                return link_text.strip()

            # 策略2：获取链接的title属性
            title = await pdf_link_element.get_attribute('title')
            if title and title.strip():
                return title.strip()

            # 策略3：查找父元素中的标题
            parent = await pdf_link_element.query_selector('..')
            if parent:
                # 查找标题元素
                title_selectors = ['h1', 'h2', 'h3', 'h4', '.title', '.name', '.product-name']
                for selector in title_selectors:
                    title_element = await parent.query_selector(selector)
                    if title_element:
                        title_text = await title_element.inner_text()
                        if title_text and title_text.strip():
                            return title_text.strip()

            # 策略4：查找同级元素中的文本
            siblings = await pdf_link_element.query_selector_all('~ *')
            for sibling in siblings:
                text = await sibling.inner_text()
                if text and text.strip() and len(text.strip()) < 100:
                    return text.strip()

            # 策略5：从PDF文件名推断
            href = await pdf_link_element.get_attribute('href')
            if href:
                filename = href.split('/')[-1]
                if filename.endswith('.pdf'):
                    # 移除文件扩展名和可能的时间戳
                    name = filename[:-4]
                    # 移除数字时间戳
                    import re
                    name = re.sub(r'-\d+$', '', name)
                    if name:
                        return name

        except Exception as e:
            logger.debug(f"提取产品名称失败: {e}")

        return "未知产品"

    async def find_products_in_page_content(self, category_name: str, category_url: str) -> List[Dict[str, Any]]:
        """在页面内容中查找产品信息"""
        products = []

        try:
            # 查找可能包含产品信息的容器
            product_containers = await self.page.query_selector_all(
                '.product-item, .item, .card, .box, [class*="product"], [class*="item"]'
            )

            for container in product_containers:
                try:
                    # 检查容器是否包含有用信息
                    text = await container.inner_text()
                    if not text or len(text.strip()) < 5:
                        continue

                    # 查找PDF链接
                    pdf_link = await container.query_selector('a[href*=".pdf"]')
                    pdf_url = ""
                    if pdf_link:
                        href = await pdf_link.get_attribute('href')
                        if href:
                            pdf_url = urljoin(self.base_url, href)

                    # 提取产品名称
                    product_name = await self.extract_product_name_from_container(container)

                    # 提取图片
                    image_url = ""
                    img = await container.query_selector('img')
                    if img:
                        src = await img.get_attribute('src')
                        if src:
                            image_url = urljoin(self.base_url, src)

                    if product_name:
                        product_info = {
                            'name': product_name,
                            'category': category_name,
                            'subcategory': category_name,
                            'description': text[:200] if len(text) > 200 else text,  # 限制描述长度
                            'pdf_url': pdf_url,
                            'image_url': image_url,
                            'scraped_at': asyncio.get_event_loop().time(),
                            'source_url': category_url
                        }

                        products.append(product_info)

                except Exception as e:
                    logger.debug(f"处理产品容器时出错: {e}")
                    continue

        except Exception as e:
            logger.error(f"查找页面产品内容失败: {str(e)}")

        return products

    async def extract_product_name_from_container(self, container) -> str:
        """从容器元素中提取产品名称"""
        try:
            # 查找标题元素
            title_selectors = ['h1', 'h2', 'h3', 'h4', '.title', '.name', '.product-name', 'strong', 'b']

            for selector in title_selectors:
                title_element = await container.query_selector(selector)
                if title_element:
                    title_text = await title_element.inner_text()
                    if title_text and title_text.strip():
                        return title_text.strip()

            # 如果没有找到标题，使用容器的文本内容
            full_text = await container.inner_text()
            if full_text:
                lines = full_text.split('\n')
                for line in lines:
                    line = line.strip()
                    if line and len(line) < 100 and len(line) > 5:
                        return line

        except Exception as e:
            logger.debug(f"从容器提取产品名称失败: {e}")

        return ""

    async def get_category_structure(self) -> List[Dict[str, Any]]:
        """获取主要分类结构"""
        categories = []

        try:
            # 等待页面加载完成
            await asyncio.sleep(3)

            # 更精确的选择器，基于实际网站结构
            category_selectors = [
                # 尝试不同的选择器模式
                'li:has-text("至璨")',
                '.nav-item:has-text("至璨")',
                '.menu-item:has-text("至璨")',
                'a:has-text("至璨")',
                '[href*="category"]:has-text("至璨")',
                # 通用选择器
                'li', 'a', 'div'
            ]

            for selector in category_selectors:
                try:
                    elements = await self.page.query_selector_all(selector)
                    logger.debug(f"选择器 {selector} 找到 {len(elements)} 个元素")

                    for element in elements:
                        try:
                            text = await element.inner_text()
                            if text and "至璨" in text:
                                # 清理文本
                                clean_text = re.sub(r'\s+', ' ', text.strip())
                                if len(clean_text) < 100:  # 避免获取到整个页面的文本
                                    categories.append({
                                        'name': clean_text,
                                        'element': element
                                    })
                        except:
                            continue

                    if categories:
                        logger.info(f"使用选择器 {selector} 找到 {len(categories)} 个分类")
                        break

                except Exception as e:
                    logger.debug(f"选择器 {selector} 查找失败: {e}")
                    continue

            # 去重并过滤
            unique_categories = []
            seen_names = set()

            for cat in categories:
                name = cat['name']
                # 进一步清理和过滤
                if (name not in seen_names and
                    len(name) > 5 and len(name) < 50 and
                    "至璨" in name and
                    not any(exclude in name.lower() for exclude in ['产品中心', 'center', 'menu'])):
                    unique_categories.append(cat)
                    seen_names.add(name)

            return unique_categories[:15]  # 限制数量，避免获取到重复或无关内容

        except Exception as e:
            logger.error(f"获取分类结构失败: {str(e)}")

        return categories
        
    async def click_category(self, category_name: str):
        """点击主分类"""
        try:
            # 清理分类名称，提取核心部分
            core_name = category_name.replace("至璨 · ", "").strip()

            # 多种点击策略
            click_selectors = [
                f'a:has-text("{core_name}")',
                f'li:has-text("{core_name}")',
                f'[data-category*="{core_name}"]',
                f'[title*="{core_name}"]'
            ]

            for selector in click_selectors:
                try:
                    element = await self.page.query_selector(selector)
                    if element:
                        await element.click()
                        logger.info(f"点击分类: {category_name} (使用选择器: {selector})")
                        await asyncio.sleep(2)  # 等待页面响应
                        return True
                except Exception as e:
                    logger.debug(f"选择器 {selector} 点击失败: {e}")
                    continue

            # 如果上述方法都失败，尝试文本匹配点击
            elements = await self.page.query_selector_all('a, li, div[onclick], [role="button"]')
            for element in elements:
                try:
                    text = await element.inner_text()
                    if text and (core_name in text or category_name in text):
                        # 检查元素是否可见和可点击
                        is_visible = await element.is_visible()
                        if is_visible:
                            await element.click()
                            logger.info(f"点击分类: {category_name} (文本匹配)")
                            await asyncio.sleep(2)
                            return True
                except:
                    continue

        except Exception as e:
            logger.error(f"点击分类失败 {category_name}: {str(e)}")

        return False
        
    async def get_subcategories(self, category_name: str) -> List[Dict[str, Any]]:
        """获取子分类"""
        subcategories = []

        try:
            # 等待子分类加载
            await asyncio.sleep(3)

            # 在这个网站中，可能没有明显的子分类结构
            # 直接查找产品项目
            product_selectors = [
                '.product-item',
                '.download-item',
                '.file-item',
                '.card',
                '.item',
                'a[href*=".pdf"]',
                '[class*="product"]',
                '[class*="download"]'
            ]

            found_products = False

            for selector in product_selectors:
                try:
                    elements = await self.page.query_selector_all(selector)
                    logger.debug(f"选择器 {selector} 找到 {len(elements)} 个元素")

                    if elements:
                        # 如果找到产品，说明这个分类下直接有产品，没有子分类
                        found_products = True
                        break

                except Exception as e:
                    logger.debug(f"产品选择器 {selector} 查找失败: {e}")
                    continue

            if found_products:
                # 如果直接找到产品，创建一个默认子分类
                subcategories.append({
                    'name': f"{category_name}_产品",
                    'element': None
                })
                logger.info(f"分类 {category_name} 下直接包含产品，创建默认子分类")
            else:
                # 尝试查找真正的子分类
                subcategory_selectors = [
                    'li li',  # 嵌套列表项
                    '.sub-item',
                    '.child-item',
                    'ul ul li',
                    '[class*="sub"] a',
                    '[class*="child"] a'
                ]

                for selector in subcategory_selectors:
                    try:
                        sub_elements = await self.page.query_selector_all(selector)

                        for element in sub_elements:
                            text = await element.inner_text()
                            if text and text.strip() and len(text.strip()) < 100:
                                clean_text = re.sub(r'\s+', ' ', text.strip())
                                subcategories.append({
                                    'name': clean_text,
                                    'element': element
                                })

                        if subcategories:
                            logger.info(f"使用选择器 {selector} 找到 {len(subcategories)} 个子分类")
                            break

                    except Exception as e:
                        logger.debug(f"子分类选择器 {selector} 查找失败: {e}")
                        continue

        except Exception as e:
            logger.error(f"获取子分类失败: {str(e)}")

        return subcategories[:10]  # 限制数量
        
    async def click_subcategory(self, subcategory_name: str):
        """点击子分类"""
        try:
            elements = await self.page.query_selector_all('*')
            for element in elements:
                try:
                    text = await element.inner_text()
                    if text and subcategory_name in text and len(text.strip()) < 100:
                        await element.click()
                        logger.info(f"点击子分类: {subcategory_name}")
                        return True
                except:
                    continue
                    
        except Exception as e:
            logger.error(f"点击子分类失败 {subcategory_name}: {str(e)}")
            
        return False
        
    async def get_products_in_subcategory(self, category_name: str, subcategory_name: str) -> List[Dict[str, Any]]:
        """获取子分类下的产品"""
        products = []

        try:
            # 等待产品列表加载
            await asyncio.sleep(3)

            # 更全面的产品选择器
            product_selectors = [
                # 直接查找包含PDF链接的元素
                'a[href*=".pdf"]',
                # 查找产品卡片
                '.product-card',
                '.product-item',
                '.download-item',
                '.file-item',
                '.card',
                '.item',
                # 查找包含下载按钮的容器
                '[class*="download"]',
                '[class*="product"]',
                # 查找图片和标题组合
                'img + h3, img + h4, img + .title',
                # 查找列表项
                'li:has(a[href*=".pdf"])',
                'div:has(a[href*=".pdf"])'
            ]

            all_found_elements = []

            for selector in product_selectors:
                try:
                    elements = await self.page.query_selector_all(selector)
                    logger.debug(f"选择器 {selector} 找到 {len(elements)} 个元素")

                    for element in elements:
                        # 检查元素是否包含有用信息
                        if await self.is_valid_product_element(element):
                            all_found_elements.append(element)

                except Exception as e:
                    logger.debug(f"产品选择器 {selector} 查找失败: {e}")
                    continue

            # 去重
            unique_elements = []
            processed_texts = set()

            for element in all_found_elements:
                try:
                    text = await element.inner_text()
                    if text and text not in processed_texts:
                        unique_elements.append(element)
                        processed_texts.add(text)
                except:
                    continue

            logger.info(f"找到 {len(unique_elements)} 个唯一产品元素")

            # 提取产品信息
            for element in unique_elements[:20]:  # 限制处理数量
                try:
                    product_info = await self.extract_product_info_from_element(element, category_name, subcategory_name)
                    if product_info:
                        products.append(product_info)
                except Exception as e:
                    logger.debug(f"提取产品信息失败: {e}")
                    continue

        except Exception as e:
            logger.error(f"获取产品列表失败: {str(e)}")

        return products

    async def is_valid_product_element(self, element) -> bool:
        """检查元素是否是有效的产品元素"""
        try:
            # 检查是否包含PDF链接
            pdf_link = await element.query_selector('a[href*=".pdf"]')
            if pdf_link:
                return True

            # 检查是否本身就是PDF链接
            href = await element.get_attribute('href')
            if href and '.pdf' in href:
                return True

            # 检查是否包含产品相关文本
            text = await element.inner_text()
            if text:
                product_keywords = ['手册', '说明书', '规格', '参数', '型号', 'PLC', 'CPU', '传感器', '模块']
                if any(keyword in text for keyword in product_keywords):
                    return True

        except:
            pass

        return False
        
    async def extract_product_info_from_element(self, element, category_name: str, subcategory_name: str) -> Optional[Dict[str, Any]]:
        """从元素中提取产品信息"""
        try:
            # 提取产品名称 - 多种策略
            name = ""
            name_selectors = ['h3', 'h4', 'h2', '.title', '.name', '.product-name', 'strong', 'b']

            for selector in name_selectors:
                try:
                    name_element = await element.query_selector(selector)
                    if name_element:
                        name = await name_element.inner_text()
                        if name and name.strip():
                            break
                except:
                    continue

            # 如果没有找到标题，使用元素的文本内容
            if not name:
                full_text = await element.inner_text()
                if full_text:
                    # 提取第一行作为名称
                    lines = full_text.split('\n')
                    for line in lines:
                        line = line.strip()
                        if line and len(line) < 100:
                            name = line
                            break

            # 提取PDF下载链接 - 多种策略
            pdf_url = ""

            # 策略1：查找PDF链接
            pdf_selectors = ['a[href*=".pdf"]', '.download-link', '.pdf-link', 'a[title*="下载"]', 'a[title*="PDF"]']

            for selector in pdf_selectors:
                try:
                    pdf_element = await element.query_selector(selector)
                    if pdf_element:
                        href = await pdf_element.get_attribute('href')
                        if href and '.pdf' in href:
                            pdf_url = urljoin(self.base_url, href)
                            break
                except:
                    continue

            # 策略2：检查元素本身是否是PDF链接
            if not pdf_url:
                try:
                    href = await element.get_attribute('href')
                    if href and '.pdf' in href:
                        pdf_url = urljoin(self.base_url, href)
                except:
                    pass

            # 策略3：查找父元素或子元素中的PDF链接
            if not pdf_url:
                try:
                    # 查找父元素
                    parent = await element.query_selector('..')
                    if parent:
                        pdf_element = await parent.query_selector('a[href*=".pdf"]')
                        if pdf_element:
                            href = await pdf_element.get_attribute('href')
                            if href:
                                pdf_url = urljoin(self.base_url, href)
                except:
                    pass

            # 提取产品图片
            image_url = ""
            try:
                img_element = await element.query_selector('img')
                if img_element:
                    src = await img_element.get_attribute('src')
                    if src:
                        image_url = urljoin(self.base_url, src)
            except:
                pass

            # 提取产品描述
            description = ""
            desc_selectors = ['.description', '.desc', 'p', '.content', '.info']

            for selector in desc_selectors:
                try:
                    desc_element = await element.query_selector(selector)
                    if desc_element:
                        desc_text = await desc_element.inner_text()
                        if desc_text and desc_text.strip() != name:
                            description = desc_text.strip()
                            break
                except:
                    continue

            # 验证提取的信息
            if name and name.strip():
                # 清理名称
                name = re.sub(r'\s+', ' ', name.strip())

                # 如果没有PDF链接，尝试构造一个可能的链接
                if not pdf_url:
                    # 这里可以根据网站规律尝试构造PDF链接
                    # 暂时跳过没有PDF链接的产品
                    logger.debug(f"产品 {name} 没有找到PDF链接")

                return {
                    'name': name,
                    'category': category_name,
                    'subcategory': subcategory_name,
                    'description': description,
                    'pdf_url': pdf_url,
                    'image_url': image_url,
                    'scraped_at': asyncio.get_event_loop().time()
                }

        except Exception as e:
            logger.debug(f"提取产品信息失败: {str(e)}")

        return None

    async def download_and_parse_pdf(self, pdf_url: str) -> Optional[str]:
        """下载并解析PDF内容"""
        if not pdf_url:
            return None

        # 检查缓存
        if pdf_url in self.pdf_cache:
            return self.pdf_cache[pdf_url]

        try:
            logger.info(f"下载PDF: {pdf_url}")

            # 下载PDF文件
            response = requests.get(pdf_url, timeout=30)
            response.raise_for_status()

            # 解析PDF内容
            pdf_content = self.parse_pdf_content(response.content)

            # 缓存结果
            self.pdf_cache[pdf_url] = pdf_content

            return pdf_content

        except Exception as e:
            logger.error(f"下载或解析PDF失败 {pdf_url}: {str(e)}")
            return None

    def parse_pdf_content(self, pdf_bytes: bytes) -> str:
        """解析PDF内容"""
        try:
            pdf_file = BytesIO(pdf_bytes)
            pdf_reader = PyPDF2.PdfReader(pdf_file)

            text_content = ""
            for page in pdf_reader.pages:
                text_content += page.extract_text() + "\n"

            return text_content

        except Exception as e:
            logger.error(f"PDF解析失败: {str(e)}")
            return ""

    def extract_specs_from_pdf(self, pdf_content: str) -> Dict[str, str]:
        """从PDF内容中提取技术规格"""
        specs = {}

        if not pdf_content:
            return specs

        try:
            # 常见的技术参数模式
            spec_patterns = [
                # 参数名：参数值
                r'([^:\n]+)[:：]\s*([^\n]+)',
                # 参数名 = 参数值
                r'([^=\n]+)[=]\s*([^\n]+)',
                # 表格形式的参数
                r'(\w+[\w\s]*)\s+([0-9]+[^\n]*)',
                # 带单位的参数
                r'([^:\n]+)[:：]\s*([0-9]+[^\n]*[A-Za-z%°℃℉]+[^\n]*)'
            ]

            for pattern in spec_patterns:
                matches = re.findall(pattern, pdf_content, re.MULTILINE)

                for key, value in matches:
                    key = key.strip()
                    value = value.strip()

                    # 过滤掉明显不是技术参数的内容
                    if (len(key) < 50 and len(value) < 200 and
                        not any(exclude in key.lower() for exclude in ['页', 'page', '图', 'fig', '表', 'table']) and
                        any(keyword in key.lower() for keyword in ['电压', '电流', '温度', '湿度', '精度', '范围', '尺寸', '重量', '功耗', '接口', '协议'])):
                        specs[key] = value

        except Exception as e:
            logger.error(f"从PDF提取技术规格失败: {str(e)}")

        return specs

    def extract_applications_from_pdf(self, pdf_content: str) -> List[str]:
        """从PDF内容中提取应用场景"""
        applications = []

        if not pdf_content:
            return applications

        try:
            # 查找应用相关的段落
            application_patterns = [
                r'应用[场景|领域|范围][：:]\s*([^\n]+)',
                r'适用[于|场景][：:]\s*([^\n]+)',
                r'典型应用[：:]\s*([^\n]+)',
                r'主要用途[：:]\s*([^\n]+)'
            ]

            for pattern in application_patterns:
                matches = re.findall(pattern, pdf_content, re.IGNORECASE)
                for match in matches:
                    # 分割应用场景
                    apps = re.split(r'[,，;；、]', match)
                    for app in apps:
                        app = app.strip()
                        if app and len(app) > 2 and len(app) < 50:
                            applications.append(app)

        except Exception as e:
            logger.error(f"从PDF提取应用场景失败: {str(e)}")

        return list(set(applications))  # 去重

    def extract_features_from_pdf(self, pdf_content: str) -> List[str]:
        """从PDF内容中提取产品特点"""
        features = []

        if not pdf_content:
            return features

        try:
            # 查找特点相关的段落
            feature_patterns = [
                r'产品特点[：:]\s*([^\n]+)',
                r'主要特[点|色][：:]\s*([^\n]+)',
                r'优势[：:]\s*([^\n]+)',
                r'特[点|色][：:]\s*([^\n]+)'
            ]

            for pattern in feature_patterns:
                matches = re.findall(pattern, pdf_content, re.IGNORECASE)
                for match in matches:
                    # 分割特点
                    feats = re.split(r'[,，;；、]', match)
                    for feat in feats:
                        feat = feat.strip()
                        if feat and len(feat) > 3 and len(feat) < 100:
                            features.append(feat)

            # 查找列表形式的特点
            bullet_patterns = [
                r'[•·▪▫]\s*([^\n]+)',
                r'[0-9]+[.)]\s*([^\n]+)',
                r'[-*]\s*([^\n]+)'
            ]

            for pattern in bullet_patterns:
                matches = re.findall(pattern, pdf_content)
                for match in matches:
                    match = match.strip()
                    if (match and len(match) > 5 and len(match) < 100 and
                        any(keyword in match.lower() for keyword in ['高', '低', '精确', '稳定', '可靠', '智能', '自动'])):
                        features.append(match)

        except Exception as e:
            logger.error(f"从PDF提取产品特点失败: {str(e)}")

        return list(set(features))  # 去重
