# 设计文档

## 概述

本系统设计为新入职销售人员提供一套完整的产品学习和销售支持解决方案。通过自动化爬取赵中AI网站数据，生成结构化的销售文档和实用表格，让新销售能够快速掌握产品知识，提高销售成功率。

核心目标：让不熟悉产品的新销售通过系统生成的文档和表格，快速成为产品专家并成功卖出产品。

## 架构

### 系统架构图

```mermaid
graph TB
    A[赵中AI网站] --> B[Playwright爬虫引擎]
    B --> C[数据提取模块]
    C --> D[数据清洗与结构化]
    D --> E[产品知识库]
    E --> F[文档生成器]
    F --> G[销售工具包]
    
    G --> H[产品分类手册]
    G --> I[客户需求匹配表]
    G --> J[销售话术库]
    G --> K[技术参数对比表]
    G --> L[常见场景解决方案]
```

### 数据流程

1. **数据采集阶段**：Playwright自动访问网站，提取产品信息
2. **数据处理阶段**：清洗、分类、结构化存储产品数据
3. **知识生成阶段**：基于产品数据生成销售支持文档
4. **工具输出阶段**：生成可直接使用的销售工具和表格

## 组件和接口

### 1. 网站爬虫模块 (WebScraper)

**职责**：自动化访问赵中AI网站，提取所有产品信息

**核心功能**：
- 使用Playwright进行页面导航和数据提取
- 识别产品分类结构
- 提取产品详细信息（名称、规格、价格、应用场景等）
- 收集所有技术指标参数（精度、量程、工作温度、功耗、尺寸、接口类型等）
- 提取产品性能参数（响应时间、稳定性、寿命等）
- 收集认证信息（CE、FCC、防护等级等）
- 处理动态加载内容和分页

**接口**：
```typescript
interface WebScraper {
  scrapeProductCategories(): Promise<ProductCategory[]>
  scrapeProductDetails(productUrl: string): Promise<ProductDetail>
  scrapeAllProducts(): Promise<ProductDatabase>
}
```

### 2. 数据处理模块 (DataProcessor)

**职责**：清洗和结构化爬取的原始数据

**核心功能**：
- 数据去重和清洗
- 产品信息标准化
- 建立产品分类体系
- 提取和标准化所有技术指标参数
- 参数单位统一化处理
- 参数范围和数值验证
- 建立参数对比基准

**接口**：
```typescript
interface DataProcessor {
  cleanProductData(rawData: RawProductData[]): CleanProductData[]
  categorizeProducts(products: CleanProductData[]): ProductCategoryTree
  extractTechnicalSpecs(product: CleanProductData): TechnicalSpecs
}
```

### 3. 销售文档生成器 (DocumentGenerator)

**职责**：基于产品数据生成销售支持文档

**核心功能**：
- 生成产品分类手册
- 创建客户需求匹配表
- 生成销售话术模板
- 创建技术参数对比表

**接口**：
```typescript
interface DocumentGenerator {
  generateProductManual(products: ProductDatabase): ProductManual
  generateMatchingMatrix(products: ProductDatabase): MatchingMatrix
  generateSalesScripts(products: ProductDatabase): SalesScriptLibrary
  generateComparisonTables(products: ProductDatabase): ComparisonTables
}
```

## 数据模型

### 产品数据模型

```typescript
interface Product {
  id: string
  name: string
  category: string
  subcategory: string
  description: string
  technicalSpecs: TechnicalSpecs
  applications: string[]
  targetCustomers: string[]
  price: PriceInfo
  images: string[]
  advantages: string[]
}

interface TechnicalSpecs {
  // 基础参数
  detectionRange?: string
  accuracy?: string
  resolution?: string
  sensitivity?: string
  
  // 工作环境参数
  workingTemperature?: string
  workingHumidity?: string
  storageTemperature?: string
  protectionLevel?: string
  
  // 电气参数
  powerConsumption?: string
  workingVoltage?: string
  outputSignal?: string
  interfaceType?: string
  
  // 物理参数
  dimensions?: string
  weight?: string
  housingMaterial?: string
  
  // 性能参数
  responseTime?: string
  stability?: string
  lifespan?: string
  repeatability?: string
  
  // 认证信息
  certifications?: string[]
  
  // 其他参数
  [key: string]: string | string[] | undefined
}

interface ProductCategory {
  name: string
  description: string
  products: Product[]
  subcategories: ProductCategory[]
}
```

### 销售工具数据模型

```typescript
interface CustomerNeedMatrix {
  detectionObject: string
  detectionDistance: string
  specialRequirements: string[]
  recommendedProducts: ProductRecommendation[]
}

interface ProductRecommendation {
  product: Product
  matchScore: number
  sellingPoints: string[]
  targetIndustries: string[]
  salesScript: string
}

interface SalesScript {
  scenario: string
  openingLine: string
  keyPoints: string[]
  objectionHandling: string[]
  closingTechnique: string
}
```

## 错误处理

### 网站访问错误
- 网络连接失败：重试机制，最多3次
- 页面加载超时：增加等待时间，记录日志
- 反爬虫机制：添加随机延迟，使用不同User-Agent

### 数据提取错误
- 页面结构变化：使用多种选择器策略
- 数据格式异常：数据验证和清洗
- 缺失关键信息：标记并记录，继续处理其他数据

### 文档生成错误
- 模板渲染失败：提供默认模板
- 数据不完整：使用占位符，标记待完善项目

## 测试策略

### 单元测试
- 数据提取函数测试
- 数据清洗逻辑测试
- 文档生成模板测试

### 集成测试
- 完整爬虫流程测试
- 数据处理管道测试
- 文档生成端到端测试

### 用户验收测试
- 销售人员使用生成文档的效果测试
- 客户需求匹配准确性测试
- 销售话术实用性测试

## 输出文档规格

### 1. 产品分类手册 (product-catalog.md)
- 完整产品分类树状结构
- 每个产品的详细介绍
- 技术规格对比表
- 应用场景说明

### 2. 客户需求快速匹配指南 (customer-matching-guide.md)
- 标准化需求确认流程
- 三步确认法：检测对象 → 检测距离 → 特殊要求
- 需求到产品的映射表

### 3. 产品推荐速查表 (product-recommendation-table.xlsx)
```
| 客户需求 | 推荐产品 | 核心卖点 | 适用行业 | 价格区间 | 竞争优势 |
```

### 4. 标准销售话术库 (sales-scripts.md)
- 开场白模板
- 产品介绍话术
- 异议处理话术
- 成交技巧

### 5. 技术参数快速对比表 (tech-comparison.xlsx)
- 同类产品完整参数对比（精度、量程、工作温度、功耗等）
- 参数优势劣势分析
- 关键指标排名
- 性价比分析
- 客户选择建议
- 参数解释说明（帮助销售理解技术术语）

### 6. 常见客户需求场景解决方案 (common-scenarios.md)
- 典型应用场景
- 推荐产品组合
- 解决方案模板

## 实施计划

### 阶段1：数据采集基础设施
- 搭建Playwright爬虫框架
- 实现基础的页面访问和数据提取
- 建立数据存储结构

### 阶段2：数据处理和分析
- 实现数据清洗和标准化
- 建立产品分类体系
- 提取关键销售信息

### 阶段3：销售工具生成
- 开发文档生成模板
- 实现各类销售工具的自动生成
- 优化输出格式和内容

### 阶段4：测试和优化
- 进行全面测试
- 根据实际使用反馈优化
- 完善错误处理和日志记录
### 7
. 产品技术指标详细手册 (technical-specifications.md)
- 每个产品的完整技术参数表
- 参数含义和重要性解释
- 与竞品的参数对比
- 参数对应的应用场景
- 客户常问参数的标准回答

### 8. 参数快速查询表 (parameter-quick-reference.xlsx)
```
| 产品型号 | 精度 | 量程 | 工作温度 | 功耗 | 尺寸 | 接口 | 认证 | 特殊优势 |
```

## 参数收集策略

### 参数分类收集
1. **核心性能参数**：精度、量程、分辨率、响应时间
2. **环境适应参数**：工作温度、湿度、防护等级
3. **电气特性参数**：功耗、电压、输出信号、接口
4. **物理特性参数**：尺寸、重量、材质
5. **可靠性参数**：寿命、稳定性、重复性
6. **认证参数**：各类认证标准、防护等级

### 参数标准化处理
- 统一单位制（米制、摄氏度等）
- 数值范围标准化
- 参数缺失标记和处理
- 参数重要性权重分配