#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
<AUTHOR> 
@license          http://www.178188.xyz
@lastmodify       2025年8月1日
模块说明: 测试新的爬虫逻辑
"""

import asyncio
import logging
import json
from pathlib import Path
import sys

# 添加src目录到路径
sys.path.append(str(Path(__file__).parent / "src"))

from zhaozhong_scraper_v2 import ZhaoZhongAIScraperV2

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('test_new_scraper.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

async def test_new_scraper():
    """测试新的爬虫逻辑"""
    scraper = ZhaoZhongAIScraperV2()
    
    try:
        # 初始化
        await scraper.initialize()
        logger.info("爬虫初始化完成")
        
        # 测试获取分类链接
        logger.info("测试获取分类链接...")
        await scraper.page.goto(f"{scraper.base_url}/product/", wait_until='networkidle')
        
        category_links = await scraper.get_category_links()
        logger.info(f"获取到 {len(category_links)} 个分类链接:")
        for link in category_links:
            logger.info(f"  - {link['name']}: {link['url']}")
        
        # 测试访问第一个分类页面
        if category_links:
            first_category = category_links[0]
            logger.info(f"\n测试访问分类页面: {first_category['name']}")
            
            await scraper.page.goto(first_category['url'], wait_until='networkidle')
            await asyncio.sleep(3)
            
            # 查找PDF链接
            pdf_links = await scraper.page.query_selector_all('a[href*=".pdf"]')
            logger.info(f"在页面中找到 {len(pdf_links)} 个PDF链接")
            
            for i, pdf_link in enumerate(pdf_links[:5]):  # 只显示前5个
                href = await pdf_link.get_attribute('href')
                text = await pdf_link.inner_text()
                logger.info(f"  PDF {i+1}: {text.strip()} -> {href}")
            
            # 测试产品提取
            products = await scraper.get_products_in_category_page(
                first_category['name'], 
                first_category['url']
            )
            
            logger.info(f"提取到 {len(products)} 个产品:")
            for product in products:
                logger.info(f"  - {product['name']}: {product['pdf_url']}")
        
        # 测试完整爬取（只爬取前2个分类）
        logger.info("\n测试完整爬取前2个分类...")
        
        all_products = []
        for category_info in category_links[:2]:  # 只测试前2个分类
            category_name = category_info['name']
            category_url = category_info['url']
            
            logger.info(f"处理分类: {category_name}")
            
            try:
                await scraper.page.goto(category_url, wait_until='networkidle')
                await asyncio.sleep(3)
                
                products = await scraper.get_products_in_category_page(category_name, category_url)
                logger.info(f"  在 {category_name} 中找到 {len(products)} 个产品")
                
                # 测试PDF下载和解析（只测试第一个产品）
                if products:
                    first_product = products[0]
                    if first_product.get('pdf_url'):
                        logger.info(f"  测试下载PDF: {first_product['pdf_url']}")
                        pdf_content = await scraper.download_and_parse_pdf(first_product['pdf_url'])
                        
                        if pdf_content:
                            logger.info(f"  PDF内容长度: {len(pdf_content)} 字符")
                            
                            # 测试信息提取
                            specs = scraper.extract_specs_from_pdf(pdf_content)
                            applications = scraper.extract_applications_from_pdf(pdf_content)
                            features = scraper.extract_features_from_pdf(pdf_content)
                            
                            logger.info(f"  提取到技术规格: {len(specs)} 项")
                            logger.info(f"  提取到应用场景: {len(applications)} 项")
                            logger.info(f"  提取到产品特点: {len(features)} 项")
                            
                            if specs:
                                logger.info("  部分技术规格:")
                                for key, value in list(specs.items())[:3]:
                                    logger.info(f"    {key}: {value}")
                            
                            if applications:
                                logger.info(f"  应用场景: {', '.join(applications[:3])}")
                            
                            if features:
                                logger.info(f"  产品特点: {', '.join(features[:3])}")
                
                all_products.extend(products)
                
            except Exception as e:
                logger.error(f"处理分类 {category_name} 时出错: {str(e)}")
                continue
        
        logger.info(f"\n测试完成！总共获取到 {len(all_products)} 个产品")
        
        # 保存测试结果
        if all_products:
            output_file = Path("test_scraper_results.json")
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(all_products, f, ensure_ascii=False, indent=2)
            logger.info(f"测试结果已保存到: {output_file}")
        
        return all_products
        
    except Exception as e:
        logger.error(f"测试过程出错: {str(e)}")
        raise
        
    finally:
        # 清理资源
        await scraper.close()
        logger.info("爬虫资源已清理")

def main():
    """主函数"""
    try:
        products = asyncio.run(test_new_scraper())
        print(f"\n✅ 测试成功！")
        print(f"📊 获取到 {len(products)} 个产品")
        
        if products:
            print(f"📝 示例产品:")
            for i, product in enumerate(products[:3]):
                print(f"  {i+1}. {product['name']}")
                print(f"     分类: {product['category']}")
                print(f"     PDF: {product['pdf_url']}")
                if product.get('technical_specs'):
                    print(f"     技术规格: {len(product['technical_specs'])} 项")
                print()
        
    except KeyboardInterrupt:
        logger.info("用户中断了程序运行")
        print("\n⚠️  程序被用户中断")
        
    except Exception as e:
        logger.error(f"程序运行失败: {str(e)}")
        print(f"\n❌ 程序运行失败: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
