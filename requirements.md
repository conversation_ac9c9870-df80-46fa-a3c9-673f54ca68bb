# 需求文档

## 介绍

本项目旨在为新入职的销售人员开发一套完整的产品分析和客户匹配系统。通过自动化分析赵中AI网站(https://www.zhaozhongai.com/)上的所有产品信息，建立全面的产品知识库和客户需求匹配机制，帮助销售人员快速了解所有产品特性并为客户提供精准的产品推荐。

## 需求

### 需求 1

**用户故事：** 作为新入职的销售人员，我希望能够全面了解网站上的所有产品分类（包括传感器、AI设备、软件解决方案等），以便我能够向客户准确介绍我们的完整产品线。

#### 验收标准

1. 当访问赵中AI网站时，系统应能够识别并提取所有产品分类信息
2. 当分析产品分类时，系统应能够提取每个类别的技术规格、应用场景和目标客户群体
3. 当完成分析后，系统应生成结构化的产品分类文档，包含详细的产品特性说明

### 需求 2

**用户故事：** 作为销售人员，我希望能够根据客户的具体需求快速匹配合适的产品（无论是硬件设备还是软件解决方案），以便提高销售效率和客户满意度。

#### 验收标准

1. 当客户提出特定需求时，系统应能够基于产品数据库提供匹配的产品推荐
2. 当进行产品推荐时，系统应提供针对性的介绍策略和销售话术
3. 当生成推荐结果时，系统应包含产品的关键卖点、适用场景和竞争优势

### 需求 3

**用户故事：** 作为销售团队，我们需要一个自动化的数据爬取工具，以便定期更新产品信息并保持数据的准确性。

#### 验收标准

1. 当运行爬取脚本时，系统应能够使用Playwright自动访问网站并提取产品信息
2. 当提取数据时，系统应能够获取产品规格参数、价格、图片等关键信息
3. 当完成爬取后，系统应将数据保存为结构化格式，便于后续分析和使用
4. 当遇到网站结构变化时，系统应具备一定的容错能力

### 需求 4

**用户故事：** 作为销售管理者，我需要一套完整的销售工具包，包括客户需求快速匹配指南、产品推荐速查表和标准销售话术，以便培训新员工并标准化销售流程。

#### 验收标准

1. 当客户咨询时，系统应提供标准化的需求确认流程：确认检测对象、确认检测距离、确认特殊要求
2. 当完成需求分析后，系统应生成产品推荐速查表，包含客户需求、推荐产品、核心卖点、适用行业的对应关系
3. 当进行产品介绍时，系统应提供标准销售话术和具体话术示例
4. 当需要技术对比时，系统应提供技术参数快速对比表
5. 当遇到常见场景时，系统应提供常见客户需求场景的标准化处理方案

### 需求 5

**用户故事：** 作为技术支持人员，我需要确保爬取的数据质量和系统的稳定性，以便为销售团队提供可靠的数据支持。

#### 验收标准

1. 当爬取数据时，系统应验证数据的完整性和准确性
2. 当发生爬取错误时，系统应记录详细的错误日志并提供恢复机制
3. 当数据更新时，系统应保留历史版本以便对比和回滚
4. 当系统运行时，应提供监控和报告功能以跟踪爬取状态
##
# 需求 6

**用户故事：** 作为销售人员，我需要一套实用的销售辅助工具，以便在客户沟通过程中快速获取准确信息并提供专业建议。

#### 验收标准

1. 当客户提出需求时，系统应提供客户需求快速匹配指南，包含标准化的问题清单
2. 当确定客户需求后，系统应生成产品推荐速查表，格式如下：
   | 客户需求 | 推荐产品 | 核心卖点 | 适用行业 |
3. 当进行产品推介时，系统应提供标准销售话术库，包含具体的话术示例
4. 当客户需要技术对比时，系统应提供技术参数快速对比功能
5. 当遇到典型应用场景时，系统应提供常见客户需求场景的解决方案模板