#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
<AUTHOR> 
@license          http://www.178188.xyz
@lastmodify       2025年8月1日
模块说明: 测试赵中AI爬虫V2
"""

import asyncio
import logging
import json
from pathlib import Path
import sys

# 添加src目录到路径
sys.path.append(str(Path(__file__).parent / "src"))

from zhaozhong_scraper_v2 import ZhaoZhongAIScraperV2

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('zhaozhong_scraper_v2.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

async def test_scraper():
    """测试爬虫功能"""
    scraper = ZhaoZhongAIScraperV2()
    
    try:
        # 初始化
        await scraper.initialize()
        logger.info("爬虫初始化完成")
        
        # 爬取所有产品
        logger.info("开始爬取产品数据...")
        products = await scraper.scrape_all_products()
        
        logger.info(f"爬取完成，共获取 {len(products)} 个产品")
        
        # 保存结果
        output_file = Path("zhaozhong_products_v2.json")
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(products, f, ensure_ascii=False, indent=2)
            
        logger.info(f"结果已保存到: {output_file}")
        
        # 显示部分结果
        if products:
            logger.info("前3个产品示例:")
            for i, product in enumerate(products[:3]):
                logger.info(f"\n产品 {i+1}:")
                logger.info(f"  名称: {product.get('name', 'N/A')}")
                logger.info(f"  分类: {product.get('category', 'N/A')} > {product.get('subcategory', 'N/A')}")
                logger.info(f"  PDF链接: {product.get('pdf_url', 'N/A')}")
                logger.info(f"  技术规格数量: {len(product.get('technical_specs', {}))}")
                logger.info(f"  应用场景数量: {len(product.get('applications', []))}")
                logger.info(f"  产品特点数量: {len(product.get('features', []))}")
                
                # 显示部分技术规格
                specs = product.get('technical_specs', {})
                if specs:
                    logger.info("  部分技术规格:")
                    for key, value in list(specs.items())[:3]:
                        logger.info(f"    {key}: {value}")
                        
                # 显示部分应用场景
                apps = product.get('applications', [])
                if apps:
                    logger.info(f"  应用场景: {', '.join(apps[:3])}")
                    
                # 显示部分特点
                features = product.get('features', [])
                if features:
                    logger.info(f"  产品特点: {', '.join(features[:3])}")
        
        return products
        
    except Exception as e:
        logger.error(f"测试过程出错: {str(e)}")
        raise
        
    finally:
        # 清理资源
        await scraper.close()
        logger.info("爬虫资源已清理")

async def test_single_category():
    """测试单个分类的爬取"""
    scraper = ZhaoZhongAIScraperV2()
    
    try:
        await scraper.initialize()
        logger.info("开始测试单个分类爬取...")
        
        # 访问下载页面
        await scraper.page.goto(scraper.download_url, wait_until='networkidle')
        
        # 获取分类结构
        categories = await scraper.get_category_structure()
        logger.info(f"发现分类: {[cat['name'] for cat in categories]}")
        
        if categories:
            # 测试第一个分类
            first_category = categories[0]
            logger.info(f"测试分类: {first_category['name']}")
            
            # 点击分类
            await scraper.click_category(first_category['name'])
            await asyncio.sleep(2)
            
            # 获取子分类
            subcategories = await scraper.get_subcategories(first_category['name'])
            logger.info(f"子分类: {[sub['name'] for sub in subcategories]}")
            
            if subcategories:
                # 测试第一个子分类
                first_sub = subcategories[0]
                logger.info(f"测试子分类: {first_sub['name']}")
                
                # 点击子分类
                await scraper.click_subcategory(first_sub['name'])
                await asyncio.sleep(2)
                
                # 获取产品
                products = await scraper.get_products_in_subcategory(
                    first_category['name'], 
                    first_sub['name']
                )
                
                logger.info(f"该子分类下的产品: {len(products)}")
                for product in products:
                    logger.info(f"  - {product.get('name', 'N/A')}: {product.get('pdf_url', 'N/A')}")
        
    except Exception as e:
        logger.error(f"单分类测试失败: {str(e)}")
        raise
        
    finally:
        await scraper.close()

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='测试赵中AI爬虫V2')
    parser.add_argument('--mode', choices=['full', 'single'], default='single',
                       help='测试模式: full=完整爬取, single=单分类测试')
    
    args = parser.parse_args()
    
    if args.mode == 'full':
        logger.info("开始完整爬取测试...")
        asyncio.run(test_scraper())
    else:
        logger.info("开始单分类测试...")
        asyncio.run(test_single_category())

if __name__ == "__main__":
    main()
