#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统测试脚本
测试爬虫和文档生成功能
"""

import asyncio
import json
import logging
from pathlib import Path
from src.scraper import <PERSON>ZhongAIScraper
from src.data_processor import DataProcessor
from src.document_generator import DocumentGenerator

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_scraper():
    """测试爬虫功能"""
    logger.info("测试爬虫功能...")
    
    scraper = ZhaoZhongAIScraper()
    await scraper.initialize()
    
    try:
        # 测试访问网站
        await scraper.page.goto(scraper.base_url, wait_until='networkidle', timeout=30000)
        logger.info("网站访问成功")
        
        # 测试获取分类
        categories = await scraper.get_product_categories()
        logger.info(f"找到 {len(categories)} 个分类: {list(categories.keys())}")
        
        # 测试爬取少量产品（限制数量避免过长时间）
        if categories:
            first_category = list(categories.items())[0]
            category_name, category_url = first_category
            logger.info(f"测试爬取分类: {category_name}")
            
            products = await scraper.scrape_category_products(category_name, category_url)
            logger.info(f"爬取到 {len(products)} 个产品")
            
            if products:
                logger.info(f"示例产品: {products[0]['name']}")
                return products[:3]  # 返回前3个产品用于测试
        
    except Exception as e:
        logger.error(f"爬虫测试失败: {str(e)}")
        return []
    finally:
        await scraper.close()
    
    return []

def test_data_processor(products_data):
    """测试数据处理功能"""
    logger.info("测试数据处理功能...")
    
    processor = DataProcessor()
    
    # 清洗数据
    clean_data = processor.clean_and_structure_data(products_data)
    logger.info(f"清洗后数据数量: {len(clean_data)}")
    
    # 分类产品
    categories = processor.categorize_products(clean_data)
    logger.info(f"产品分类数量: {len(categories)}")
    
    # 提取技术规格
    tech_specs = processor.extract_technical_specifications(clean_data)
    logger.info(f"技术规格分析完成")
    
    return clean_data, categories, tech_specs

async def test_document_generator(clean_data, categories, tech_specs):
    """测试文档生成功能"""
    logger.info("测试文档生成功能...")
    
    output_dir = Path("test_output")
    output_dir.mkdir(exist_ok=True)
    
    generator = DocumentGenerator(clean_data, categories, tech_specs)
    
    try:
        # 生成所有文档
        await generator.generate_all_documents(output_dir)
        logger.info("文档生成完成")
        
        # 检查生成的文件
        files = list(output_dir.glob("*"))
        logger.info(f"生成了 {len(files)} 个文件:")
        for file in files:
            logger.info(f"  - {file.name}")
            
    except Exception as e:
        logger.error(f"文档生成失败: {str(e)}")

async def main():
    """主测试函数"""
    logger.info("开始系统测试...")
    
    # 1. 测试爬虫
    products_data = await test_scraper()
    
    if not products_data:
        logger.warning("爬虫测试失败，使用模拟数据进行后续测试")
        # 创建模拟数据
        products_data = [
            {
                'name': '温度传感器 TH-001',
                'category': '温度传感器',
                'description': '高精度数字温度传感器，适用于工业环境监测',
                'price': '¥299',
                'url': 'https://example.com/th001',
                'images': [],
                'technical_specs': {
                    '测量范围': '-40°C ~ +85°C',
                    '精度': '±0.5°C',
                    '分辨率': '0.1°C',
                    '工作电压': '3.3V ~ 5V'
                },
                'applications': ['工业自动化', '环境监测', '智能家居'],
                'features': ['高精度测量', '数字输出', '低功耗设计'],
                'scraped_at': 1234567890
            },
            {
                'name': '压力传感器 PR-002',
                'category': '压力传感器',
                'description': '工业级压力传感器，适用于液体和气体压力测量',
                'price': '¥599',
                'url': 'https://example.com/pr002',
                'images': [],
                'technical_specs': {
                    '测量范围': '0 ~ 10MPa',
                    '精度': '±0.25%FS',
                    '输出信号': '4-20mA',
                    '工作温度': '-20°C ~ +80°C'
                },
                'applications': ['工业自动化', '流体控制', '安全监测'],
                'features': ['高稳定性', '抗冲击', '防腐蚀'],
                'scraped_at': 1234567890
            }
        ]
    
    # 2. 测试数据处理
    clean_data, categories, tech_specs = test_data_processor(products_data)
    
    # 3. 测试文档生成
    await test_document_generator(clean_data, categories, tech_specs)
    
    logger.info("系统测试完成！")

if __name__ == "__main__":
    asyncio.run(main())