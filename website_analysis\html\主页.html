<!DOCTYPE html><html lang="en"><head>
    <meta charset="UTF-8">
    <meta http-equiv="Cache-Control" content="no-transform">
    <meta http-equiv="Cache-Control" content="no-siteapp">
    <meta name="applicable-device" content="mobile">
    <meta name="renderer" content="webkit">
    <meta content="email=no" name="format-detection">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta http-equiv="Content-Language" content="zh-CN">
    <meta content=" " name="design">
    <meta name="viewport" content="width=device-width,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="format-detection" content="telephone = no">
    
    <!-- 字体图标 -->
<link rel="shortcut icon" href="/favicon.ico">
<link rel="stylesheet" href="https://at.alicdn.com/t/font_3312555_r19ypx0xkc.css">
<link href="/template/default/pc/static/css/swiper.min.css" rel="stylesheet" media="screen" type="text/css">
<link href="/template/default/pc/static/css/animate.min.css" rel="stylesheet" media="screen" type="text/css">
<link href="/template/default/pc/static/css/iconfont.css" rel="stylesheet" media="screen" type="text/css">
<link href="/template/default/pc/static/css/layui.css" rel="stylesheet" media="screen" type="text/css">
<link href="/template/default/pc/static/css/style.css" rel="stylesheet" media="screen" type="text/css">
<script src="https://hm.baidu.com/hm.js?71717ad93e343f6e3d55e6a8e630767f"></script><script src="https://zz.bdstatic.com/linksubmit/push.js"></script><script language="javascript" type="text/javascript" src="/template/default/pc/static/js/jquery-3.3.1.min.js"></script>
<script language="javascript" type="text/javascript" src="/template/default/pc/static/js/swiper4.5.3.min.js"></script>
<script language="javascript" type="text/javascript" src="/template/default/pc/static/js/wow.js"></script>
<script language="javascript" type="text/javascript" src="/template/default/pc/static/js/common.js"></script>
<script language="javascript" type="text/javascript" src="/template/default/pc/static/js/layui.js"></script><link id="layuicss-laydate" rel="stylesheet" href="https://www.zhaozhongai.com/template/default/pc/static/js/css/modules/laydate/default/laydate.css?v=5.3.1" media="all"><link id="layuicss-layer" rel="stylesheet" href="https://www.zhaozhongai.com/template/default/pc/static/js/css/modules/layer/default/layer.css?v=3.5.1" media="all"><link id="layuicss-skincodecss" rel="stylesheet" href="https://www.zhaozhongai.com/template/default/pc/static/js/css/modules/code.css?v=2" media="all">
<script language="javascript" type="text/javascript" src="/template/default/pc/static/js/common1.js"></script>
<script language="javascript" type="text/javascript" src="/template/default/pc/static/js/vue.min.js"></script>
<script language="javascript" type="text/javascript" src="/template/default/pc/static/js/gsap.min.js"></script>
<script language="javascript" type="text/javascript" src="/template/default/pc/static/js/gsap.registerplugin.js"></script>
<script language="javascript" type="text/javascript" src="/template/default/pc/static/js/tweenmax.min.js"></script>
<script language="javascript" type="text/javascript" src="/template/default/pc/static/js/jquery.superscrollorama.js"></script>
<script language="javascript" type="text/javascript" src="/template/default/pc/static/js/common2.js"></script>


<!-- 分享 -->
    <style>
        .layui-laypage .layui-laypage-prev i, .layui-laypage .layui-laypage-next i{
            vertical-align: top;
            font-size: 14px;
        }

       
        .layui-laypage .layui-laypage-prev, .layui-laypage .layui-laypage-next{
            width: 30px;
            padding: 0;
            border: 1px solid #494b4c;
            border-radius: 0;
            border-radius: 0 !important;
        }

        .layui-laypage .layui-laypage-prev:hover, .layui-laypage .layui-laypage-next:hover {
            background: #2b37d8;
            border-color: #2b37d8;
            color: #fff !important;
        }

        .layui-laypage .layui-laypage-prev:hover i, .layui-laypage .layui-laypage-next:hover i {
            border-color: #fff !important;
        }

        .layui-laypage a, .layui-laypage span {
            color: #333  !important;
        }


    </style>

	<script>
		window.conf = {"ROOT":"","STYLE":"/wstmart/home/<USER>/default","APP":"","STATIC":"/static","SUFFIX":"html","SMS_VERFY":"","SMS_OPEN":"","GOODS_LOGO":"","SHOP_LOGO":"","MALL_LOGO":"upload/sysconfigs/2022-06/62a3f784da502.png","USER_LOGO":"","IS_LOGIN":"0","TIME_TASK":"1","ROUTES":'{"admin\/index\/login":"login_3","home\/index\/index":"\/","home\/product\/index":"product\/[:id]","home\/product\/index2":"productlist\/[:id]","home\/product\/detail":"productinfo<id>","home\/solution\/index":"solution\/[:id]","home\/solution\/detail":"casesinfo<id>","home\/news\/index":"news\/[:id]","home\/news\/detail":"newsinfo<id>","home\/about\/faq":"faq","home\/download\/index":"download\/[:id]","home\/about\/index":"about","home\/about\/service":"service","home\/about\/make":"make","home\/about\/feedback":"feedback","home\/contact\/index":"contact","home\/about\/join":"join","home\/about\/order":"order","home\/about\/trial":"trial","home\/about\/stop":"stop","home\/about\/ability":"ability","home\/honor\/index":"honor\/[:id]","home\/solution\/cases":"cases\/[:id]","home\/index\/Privacy_Policy":"privacyPolicy","home\/index\/Legal_Notices":"legalNotices","home\/search\/index":"search\/[:keys]","home\/search\/tags":"tag<id>","home\/search\/tagsAll":"tagAll","home\/sitmap\/index":"sitemap"}'}
	</script>
<title>浙江兆众智能电子科技有限公司-“至璨/ZHICAN”工业品牌旗舰系列</title>
<meta name="description" content="浙江兆众智能电子科技有限公司深耕技术研发与产品创新领域，致力于实现国产工业品牌完美替代进口品牌战略，全力打造“至璨/ZHICAN”工业品牌旗舰系列，以卓越品质彰显中国智造的力量与魅力。">
<meta name="keywords" content="浙江兆众智能">


<script type="text/javascript" src="/public/static/common/js/ey_global.js?v=v1.7.4"></script>
</head>

<body style="">

 <div id="header">
    <!-- 头部 PC -->

<div class="header_yt0516 on">
	<!-- 头部内容 -->
	<div class="header_main w1800 ">
		<div class="logo">
		<a href="https://www.zhaozhongai.com"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240226/1-240226113354108.png" alt="“至璨/ZHICAN”工业品牌旗舰系列"><img class="active" src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240229/1-2402291641263c.png" alt="“至璨/ZHICAN”工业品牌旗舰系列"></a>
		</div>
		<div class="logo_text">专注于工业智能化、信息化、自动化及AI智能等领域</div>
		<div class="column qtwy-header">
			<!-- PC导航 -->
			<div class="nav">
				<ul>
<li><a class="active" href="https://www.zhaozhongai.com">首页</a></li>


<li><a href="/solution/" class="">应用案例</a>

<div class="nav_menu">
	<div class="center">
		<div class="content w1600">
			<div class="c_left">
				<div class="info">
					<div class="title">应用案例</div>
					<div class="txt">根据用户需求选择适合您的产品；帮助用户创造价值，用户因为我们而实现更大的梦想。
</div>
				</div>
				<div class="list">
									<div class="link"><a href="/cases1/">行业应用案例<div class="icon"></div></a></div>
									<div class="link"><a href="/cases2/">检测实验应用教学<div class="icon"></div></a></div>
									<div class="link"><a href="/cases3/">产品拓扑图<div class="icon"></div></a></div>
								</div>
			</div>
			<div class="img">
				<div class="pb">
					<div class="ab">
					<a href="/solution/"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240226/1-240226154R2L9.png" alt="/solution/"></a>
					</div>
				</div>
			</div>
				<div class="menu_list">
					<div class="menu_title">其他咨询服务？</div>
					<div class="items">
												<div class="item">
							<a href="/mianfeishiyong/">
								<div class="icon"><img src="/static/picture/icon_ser_4.png" alt="免费试用"></div>
								<div class="title">免费试用</div>
							</a>
						</div>
												<div class="item">
							<a href="/product/">
								<div class="icon"><img src="/static/picture/icon_ser_2.png" alt="产品中心"></div>
								<div class="title">产品中心</div>
							</a>
						</div>
												<div class="item">
							<a href="/dinggouzixun/">
								<div class="icon"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240103/1-2401031G42TQ.png" alt="订购咨询"></div>
								<div class="title">订购咨询</div>
							</a>
						</div>
											</div>
				</div>
</div>
<div class="r_bg"></div>
</div>
</div>


</li>




<li><a href="/product/" class="">产品中心</a>

<div class="nav_menu">
	<div class="center">
		<div class="content w1600">
			<div class="c_left">
				<div class="info">
					<div class="title">产品中心</div>
					<div class="txt">兆众智能一直遵循产品质量是设计进去，制造出来的品质理念；不断钻研专业技术，加快工业自动化的国产化进程。</div>
				</div>
				<div class="list">
									<div class="link"><a href="/productlist1/">至璨 · CPU单元<div class="icon"></div></a></div>
									<div class="link"><a href="/productlist2/">至璨 · 总线耦合器<div class="icon"></div></a></div>
									<div class="link"><a href="/mokuai/">至璨 · IO模块<div class="icon"></div></a></div>
									<div class="link"><a href="/jiejinchuanganqi/">至璨 · 接近传感器<div class="icon"></div></a></div>
									<div class="link"><a href="/guangdianchuanganqi/">至璨 · 光电传感器<div class="icon"></div></a></div>
									<div class="link"><a href="/-chong--lu-juan----hong--hu-dian-/">至璨 · 继电器<div class="icon"></div></a></div>
									<div class="link"><a href="/jiguangguangdianchuanganqi/">至璨 · 激光光电传感器<div class="icon"></div></a></div>
									<div class="link"><a href="/jiguangweiyichuanganqi/">至璨 · 激光位移传感器<div class="icon"></div></a></div>
									<div class="link"><a href="/sebiaochuanganqi/">至璨 · 色标传感器<div class="icon"></div></a></div>
									<div class="link"><a href="/guangxianchuanganqi/">至璨 · 光纤传感器<div class="icon"></div></a></div>
									<div class="link"><a href="/-chong--lu-huan--liao---/">至璨 · 微动/行程/限位开关<div class="icon"></div></a></div>
									<div class="link"><a href="/-chong--lu-bing--liao-donghuan-----/">至璨 · 管道液位传感器<div class="icon"></div></a></div>
								</div>
			</div>
			<div class="img">
				<div class="pb">
					<div class="ab">
					<a href="/product/"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240226/1-240226154R2L9.png" alt="/product/"></a>
					</div>
				</div>
			</div>
				<div class="menu_list">
					<div class="menu_title">其他咨询服务？</div>
					<div class="items">
												<div class="item">
							<a href="/mianfeishiyong/">
								<div class="icon"><img src="/static/picture/icon_ser_4.png" alt="免费试用"></div>
								<div class="title">免费试用</div>
							</a>
						</div>
												<div class="item">
							<a href="/product/">
								<div class="icon"><img src="/static/picture/icon_ser_2.png" alt="产品中心"></div>
								<div class="title">产品中心</div>
							</a>
						</div>
												<div class="item">
							<a href="/dinggouzixun/">
								<div class="icon"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240103/1-2401031G42TQ.png" alt="订购咨询"></div>
								<div class="title">订购咨询</div>
							</a>
						</div>
											</div>
				</div>
</div>
<div class="r_bg"></div>
</div>
</div>


</li>




<li><a href="/service/" class="">服务支持</a>


<ul>
<li><a href="/download/">资料下载</a></li>
<li><a href="/fuwujieshao/">服务介绍</a></li>
<li><a href="/dinggouzixun/">订购咨询</a></li>
<li><a href="/mianfeishiyong/">免费试用</a></li>
<li><a href="/changjianwenti/">常见问题</a></li>
<li><a href="/zaixianliuyan/">在线留言</a></li>
</ul>

</li>




<li><a href="/make/" class="">研发制造</a>


<ul>
<li><a href="/yanfashili/">研发实力</a></li>
<li><a href="/kehudingzhi/">客户定制</a></li>
<li><a href="/jingmizhizao/">精密制造</a></li>
<li><a href="/pinzhibaozhang/">品质保障</a></li>
</ul>

</li>




<li><a href="/about/" class="">关于我们</a>


<ul>
<li><a href="/abouts/">公司简介</a></li>
<li><a href="/qiyewenhua/">企业文化</a></li>
<li><a href="/fazhanlicheng/">发展历程</a></li>
<li><a href="/zuzhijiagou/">组织架构</a></li>
<li><a href="/join/">招贤纳士</a></li>
<li><a href="/News/"> 新闻中心</a></li>
</ul>

</li>




<li><a href="/contact1/" class="">联系我们</a>


<ul>
<li><a href="/contact/">联系方式</a></li>
<li><a href="/guestbook/">在线留言</a></li>
</ul>

</li>




	
</ul>
</div>
			<!-- 电话 -->
			<div class="phone">
				<div class="center">
				    <div class="icon"><i class="iconfont icon_dianhua"></i></div>
					<div class="number"><span>0571-88640980</span></div>
				</div>
			</div>
			<!-- 语言 -->
			<div class="lang">
				<div class="center">
					<div class="icon"></div>
									</div>
			</div>
			<!-- 菜单 -->
			<div class="menu">
				<div class="icona" id="menuBtn"><i class="iconfont icon_sousuo"></i></div>
				<div class="menu_box" id="menuList">
					<div class="center w1600">
						<div class="menu_info">
							<div class="info_title"><span>感知世界，连接未来</span></div>
							<div class="items">
								<ul>
									<li>
										<div class="head">
											<div class="icon"><img src="/static/picture/icon_contact_address.png" alt="“至璨/ZHICAN”工业品牌旗舰系列"><span class="imgmid"></span></div>
											<div class="title">联系地址：</div>
										</div>
										<div class="txt">浙江省杭州市余杭区中泰工业区甲楠智创中心A座5层</div>
									</li>
									<li>
										<div class="head">
											<div class="icon"><img src="/static/picture/icon_contact_mail.png" alt="“至璨/ZHICAN”工业品牌旗舰系列"><span class="imgmid"></span></div>
											<div class="title">联系邮箱：</div>
										</div>
										<div class="txt"><EMAIL></div>
									</li>
									<li>
										<div class="head">
											<div class="icon"><img src="/static/picture/icon_contact_fax.png" alt="“至璨/ZHICAN”工业品牌旗舰系列"><span class="imgmid"></span></div>
											<div class="title">公司传真：</div>
										</div>
										<div class="txt">公司传真</div>
									</li>
									<li>
										<div class="head">
											<div class="icon"><img src="/static/picture/icon_contact_info.png" alt="“至璨/ZHICAN”工业品牌旗舰系列"><span class="imgmid"></span></div>
											<div class="title">售前咨询：</div>
										</div>
										<div class="txt">0571-88640980</div>
									</li>
								</ul>
							</div>
						
   
							<div class="search_form">
							 <form method="get" action="/search.html" class="layui-form">
	        <input type="hidden" name="method" value="1" style="">									<input type="text" name="keywords" id="keys" class="ipt layui-input" placeholder="请输入搜索关键词..." style="">
									<button class="layui-btn" type="submit"><i class="iconfont icon_sousuo"></i>立即搜索</button>
    </form>
							</div>

						</div>
						<div class="menu_list">
							<div class="menu_title">其他咨询服务？</div>
							<div class="items">
												<div class="item">
							<a href="/mianfeishiyong/">
								<div class="icon"><img src="/static/picture/icon_ser_4.png" alt="免费试用"></div>
								<div class="title">免费试用</div>
							</a>
						</div>
												<div class="item">
							<a href="/product/">
								<div class="icon"><img src="/static/picture/icon_ser_2.png" alt="产品中心"></div>
								<div class="title">产品中心</div>
							</a>
						</div>
												<div class="item">
							<a href="/dinggouzixun/">
								<div class="icon"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240103/1-2401031G42TQ.png" alt="订购咨询"></div>
								<div class="title">订购咨询</div>
							</a>
						</div>
						
								
								
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	
	
	</div>
<script type="text/javascript">
	$(document).ready(() => {
		layui.use('form', function() {
			var form = layui.form;
			let c = 'active';
			$('#menuBtn').on('click', function() {
				if(!$(this).hasClass(c)){
					$(this).addClass(c);
					$('#menuList').stop().slideDown(400);
				} else {
					$(this).removeClass(c);
					$('#menuList').stop().slideUp(400);
				}
			});

			//监听提交
			form.on('submit(formDemo)', function(data) {
				layer.msg(JSON.stringify(data.field));
				return false;
			});
		});
	});
	function headerTop(){
		var headerBox = $('.header_yt0516'),
			c = 'active';
		if(!headerBox.hasClass(c)){
			function headerFixe() {
				headerBox.hover(function() {
					headerBox.addClass('on');
				}, function() {
					if(!$('#menuBtn').hasClass(c)){
						headerBox.removeClass('on');
					}
				});
				let top = $(document).scrollTop();
				if(top > 0){
					headerBox.addClass(c);
				} else {
					headerBox.removeClass(c);
				}
			}
			headerFixe();
			$(document).scroll( () => headerFixe() );
		}
		else{
			headerBox.after('<div class="header_nbsp" style="width: 100%; height: ' + headerBox.outerHeight() + 'px;"></div>');
			function isActive(){
				if( $(document).scrollTop() > 0){
					$('#pageSubnav').addClass(c);
				} else {
					$('#pageSubnav').removeClass(c);
				}
			}
			isActive();
			$(document).scroll( () => isActive() );
		}
	}
	headerTop();
	
	
</script>


<!-- 头部 手机版 -->
<div class="head_wap">
	<div class="h_top">
		<a href="https://www.zhaozhongai.com" class="logo">
			<img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240429/1-240429131F1K1.png" alt="“至璨/ZHICAN”工业品牌旗舰系列" title="“至璨/ZHICAN”工业品牌旗舰系列">
		</a>
		<a class="open_nav"><i></i><i></i><i></i></a>
	</div>
	<div class="h_bot">
		<ul class="h_nav">
			<li class="on">
				<div class="a1">
					<div class="top"><a href="https://www.zhaozhongai.com">首页</a></div>
				</div>
			</li>
			
 <li class="">
<div class="a1">
<div class="top"><a href="/solution/">应用案例</a><i class="i1 iconfont icon_arrow-right"></i></div>

<div class="box">
<div class="a2">
<div class="top2"><a href="/cases1/">行业应用案例</a><i class="i2 iconfont icon_arrow-right"></i></div>
</div>
<div class="a2">
<div class="top2"><a href="/cases2/">检测实验应用教学</a><i class="i2 iconfont icon_arrow-right"></i></div>
</div>
<div class="a2">
<div class="top2"><a href="/cases3/">产品拓扑图</a><i class="i2 iconfont icon_arrow-right"></i></div>
</div>


</div>
</div>
</li>
<li class="">
<div class="a1">
<div class="top"><a href="/product/">产品中心</a><i class="i1 iconfont icon_arrow-right"></i></div>

<div class="box">
<div class="a2">
<div class="top2"><a href="/productlist1/">至璨 · CPU单元</a><i class="i2 iconfont icon_arrow-right"></i></div>
</div>
<div class="a2">
<div class="top2"><a href="/productlist2/">至璨 · 总线耦合器</a><i class="i2 iconfont icon_arrow-right"></i></div>
</div>
<div class="a2">
<div class="top2"><a href="/mokuai/">至璨 · IO模块</a><i class="i2 iconfont icon_arrow-right"></i></div>
</div>
<div class="a2">
<div class="top2"><a href="/jiejinchuanganqi/">至璨 · 接近传感器</a><i class="i2 iconfont icon_arrow-right"></i></div>
</div>
<div class="a2">
<div class="top2"><a href="/guangdianchuanganqi/">至璨 · 光电传感器</a><i class="i2 iconfont icon_arrow-right"></i></div>
</div>
<div class="a2">
<div class="top2"><a href="/-chong--lu-juan----hong--hu-dian-/">至璨 · 继电器</a><i class="i2 iconfont icon_arrow-right"></i></div>
</div>
<div class="a2">
<div class="top2"><a href="/jiguangguangdianchuanganqi/">至璨 · 激光光电传感器</a><i class="i2 iconfont icon_arrow-right"></i></div>
</div>
<div class="a2">
<div class="top2"><a href="/jiguangweiyichuanganqi/">至璨 · 激光位移传感器</a><i class="i2 iconfont icon_arrow-right"></i></div>
</div>
<div class="a2">
<div class="top2"><a href="/sebiaochuanganqi/">至璨 · 色标传感器</a><i class="i2 iconfont icon_arrow-right"></i></div>
</div>
<div class="a2">
<div class="top2"><a href="/guangxianchuanganqi/">至璨 · 光纤传感器</a><i class="i2 iconfont icon_arrow-right"></i></div>
</div>
<div class="a2">
<div class="top2"><a href="/-chong--lu-huan--liao---/">至璨 · 微动/行程/限位开关</a><i class="i2 iconfont icon_arrow-right"></i></div>
</div>
<div class="a2">
<div class="top2"><a href="/-chong--lu-bing--liao-donghuan-----/">至璨 · 管道液位传感器</a><i class="i2 iconfont icon_arrow-right"></i></div>
</div>


</div>
</div>
</li>
<li class="">
<div class="a1">
<div class="top"><a href="/service/">服务支持</a><i class="i1 iconfont icon_arrow-right"></i></div>

<div class="box">
<div class="a2">
<div class="top2"><a href="/download/">资料下载</a><i class="i2 iconfont icon_arrow-right"></i></div>
</div>
<div class="a2">
<div class="top2"><a href="/fuwujieshao/">服务介绍</a><i class="i2 iconfont icon_arrow-right"></i></div>
</div>
<div class="a2">
<div class="top2"><a href="/dinggouzixun/">订购咨询</a><i class="i2 iconfont icon_arrow-right"></i></div>
</div>
<div class="a2">
<div class="top2"><a href="/mianfeishiyong/">免费试用</a><i class="i2 iconfont icon_arrow-right"></i></div>
</div>
<div class="a2">
<div class="top2"><a href="/changjianwenti/">常见问题</a><i class="i2 iconfont icon_arrow-right"></i></div>
</div>
<div class="a2">
<div class="top2"><a href="/zaixianliuyan/">在线留言</a><i class="i2 iconfont icon_arrow-right"></i></div>
</div>


</div>
</div>
</li>
<li class="">
<div class="a1">
<div class="top"><a href="/make/">研发制造</a><i class="i1 iconfont icon_arrow-right"></i></div>

<div class="box">
<div class="a2">
<div class="top2"><a href="/yanfashili/">研发实力</a><i class="i2 iconfont icon_arrow-right"></i></div>
</div>
<div class="a2">
<div class="top2"><a href="/kehudingzhi/">客户定制</a><i class="i2 iconfont icon_arrow-right"></i></div>
</div>
<div class="a2">
<div class="top2"><a href="/jingmizhizao/">精密制造</a><i class="i2 iconfont icon_arrow-right"></i></div>
</div>
<div class="a2">
<div class="top2"><a href="/pinzhibaozhang/">品质保障</a><i class="i2 iconfont icon_arrow-right"></i></div>
</div>


</div>
</div>
</li>
<li class="">
<div class="a1">
<div class="top"><a href="/about/">关于我们</a><i class="i1 iconfont icon_arrow-right"></i></div>

<div class="box">
<div class="a2">
<div class="top2"><a href="/abouts/">公司简介</a><i class="i2 iconfont icon_arrow-right"></i></div>
</div>
<div class="a2">
<div class="top2"><a href="/qiyewenhua/">企业文化</a><i class="i2 iconfont icon_arrow-right"></i></div>
</div>
<div class="a2">
<div class="top2"><a href="/fazhanlicheng/">发展历程</a><i class="i2 iconfont icon_arrow-right"></i></div>
</div>
<div class="a2">
<div class="top2"><a href="/zuzhijiagou/">组织架构</a><i class="i2 iconfont icon_arrow-right"></i></div>
</div>
<div class="a2">
<div class="top2"><a href="/join/">招贤纳士</a><i class="i2 iconfont icon_arrow-right"></i></div>
</div>
<div class="a2">
<div class="top2"><a href="/News/"> 新闻中心</a><i class="i2 iconfont icon_arrow-right"></i></div>
</div>


</div>
</div>
</li>
<li class="">
<div class="a1">
<div class="top"><a href="/contact1/">联系我们</a><i class="i1 iconfont icon_arrow-right"></i></div>

<div class="box">
<div class="a2">
<div class="top2"><a href="/contact/">联系方式</a><i class="i2 iconfont icon_arrow-right"></i></div>
</div>
<div class="a2">
<div class="top2"><a href="/guestbook/">在线留言</a><i class="i2 iconfont icon_arrow-right"></i></div>
</div>


</div>
</div>
</li>
	
		</ul>
		
		<div class="h_lan">
					</div>
	</div>
</div>
<div class="header_d"></div>
<script>
	// 移动端
	$('.open_nav').click(function() {
		$(this).toggleClass('on');
		$('.h_bot').slideToggle();
	})

	$('.h_bot .a1 .i1').click(function() {
		$(this).parents('.top').toggleClass('on');
		$(this).parents('.a1').find('.box').slideToggle();
		$(this).parents('li').siblings().removeClass('on').find('.top').removeClass('on');
		$(this).parents('li').siblings().find('.box').slideUp();
		$(this).parents('li').siblings().find('.top2').removeClass('on');
		$(this).parents('li').siblings().find('.box2').slideUp();
	})
	$('.h_bot .a2 .i2').click(function() {
		$(this).parents('.top2').toggleClass('on');
		$(this).parents('.a2').find('.box2').slideToggle();
		$(this).parents('.a2').siblings().find('.top2').removeClass('on');
		$(this).parents('.a2').siblings().find('.box2').slideUp();
	})
</script>

</div>




	<!-- 首页banner -->
 
	<div class="idx_banner">

		<div class="banner_img swiper-container-fade swiper-container-initialized swiper-container-horizontal" style="opacity: 1;">

			<div class="swiper-wrapper" style="transition-duration: 0ms;"><div class="swiper-slide swiper-slide-duplicate" data-swiper-slide-index="7" style="width: 1270px; transition-duration: 0ms; opacity: 0; transform: translate3d(0px, 0px, 0px);">
                     <a href="https://www.zhaozhongai.com/mokuai/" target="_self"><img style="width:100%" src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240328/1-24032Q1023K02.png" alt=""></a>
				</div>

								<div class="swiper-slide" data-swiper-slide-index="0" style="width: 1270px; transition-duration: 0ms; opacity: 0; transform: translate3d(-1270px, 0px, 0px);">
                     <a href="https://www.zhaozhongai.com/jiejinchuanganqi/" target="_self"><img style="width:100%" src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20250507/1-25050G61144O4.gif" alt=""></a>
				</div>
               				<div class="swiper-slide swiper-slide-prev" data-swiper-slide-index="1" style="width: 1270px; transition-duration: 0ms; opacity: 0; transform: translate3d(-2540px, 0px, 0px);">
                     <a href="https://www.zhaozhongai.com/sebiaochuanganqi/" target="_self"><img style="width:100%" src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20250507/1-25050GQ01bF.gif" alt=""></a>
				</div>
               				<div class="swiper-slide swiper-slide-active" data-swiper-slide-index="2" style="width: 1270px; transition-duration: 0ms; opacity: 1; transform: translate3d(-3810px, 0px, 0px);">
                     <a href="https://www.zhaozhongai.com/jiguangweiyichuanganqi/" target="_self"><img style="width:100%" src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240717/1-240GF93153964.gif" alt=""></a>
				</div>
               				<div class="swiper-slide swiper-slide-next" data-swiper-slide-index="3" style="width: 1270px; transition-duration: 0ms; opacity: 0; transform: translate3d(-5080px, 0px, 0px);">
                     <a href="https://www.zhaozhongai.com/guangxianchuanganqi/" target="_self"><img style="width:100%" src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20250507/1-25050G6121R13.gif" alt=""></a>
				</div>
               				<div class="swiper-slide" data-swiper-slide-index="4" style="width: 1270px; transition-duration: 0ms; opacity: 0; transform: translate3d(-6350px, 0px, 0px);">
                     <a href="https://www.zhaozhongai.com/jiguangguangdianchuanganqi/" target="_self"><img style="width:100%" src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240723/1-240H3102001N7.jpg" alt=""></a>
				</div>
               				<div class="swiper-slide" data-swiper-slide-index="5" style="width: 1270px; transition-duration: 0ms; opacity: 0; transform: translate3d(-7620px, 0px, 0px);">
                     <a href="https://www.zhaozhongai.com/productlist2/" target="_self"><img style="width:100%" src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240723/1-240H3101H5325.png" alt=""></a>
				</div>
               				<div class="swiper-slide" data-swiper-slide-index="6" style="width: 1270px; transition-duration: 0ms; opacity: 0; transform: translate3d(-8890px, 0px, 0px);">
                     <a href="https://www.zhaozhongai.com/mokuai/" target="_self"><img style="width:100%" src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240723/1-240H3102016293.png" alt=""></a>
				</div>
               				<div class="swiper-slide" data-swiper-slide-index="7" style="width: 1270px; transition-duration: 0ms; opacity: 0; transform: translate3d(-10160px, 0px, 0px);">
                     <a href="https://www.zhaozhongai.com/mokuai/" target="_self"><img style="width:100%" src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240328/1-24032Q1023K02.png" alt=""></a>
				</div>
               
				
			<div class="swiper-slide swiper-slide-duplicate" data-swiper-slide-index="0" style="width: 1270px; transition-duration: 0ms; opacity: 0; transform: translate3d(-11430px, 0px, 0px);">
                     <a href="https://www.zhaozhongai.com/jiejinchuanganqi/" target="_self"><img style="width:100%" src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20250507/1-25050G61144O4.gif" alt=""></a>
				</div></div>

		<span class="swiper-notification" aria-live="assertive" aria-atomic="true"></span></div>

	

		<div class="banner_foot" id="banner_foot" style="opacity: 1;">

			<div class="w1600">

				<div class="on_down">

					<div class="icon"><i></i></div>

					<div class="txt">滑动了解更多</div>

				</div>

				<div class="swiper_pager">

					<div class="banner_btn prev" tabindex="0" role="button" aria-label="Previous slide"><i class="iconfont icon_arrow-left"></i></div>

					<div class="banner_btn play"><i></i><i></i></div>

					<div class="banner_btn next" tabindex="0" role="button" aria-label="Next slide"><i class="iconfont icon_arrow-right"></i></div>

					<div class="swiper-pagination-1"><div class="center swiper-pagination-bullets"><span class="swiper-pagination-bullet">1</span><span class="swiper-pagination-bullet">2</span><span class="swiper-pagination-bullet swiper-pagination-bullet-active">3</span><span class="swiper-pagination-bullet">4</span><span class="swiper-pagination-bullet">5</span><span class="swiper-pagination-bullet">6</span><span class="swiper-pagination-bullet">7</span><span class="swiper-pagination-bullet">8</span></div></div>

					<div class="swiper-pagination-2"><div class="center"><div class="active">3</div><div class="line">/</div><div class="number">8</div></div></div>

				</div>

			</div>

		</div>

	</div>

	<script type="text/javascript">
		function bannerSwiper() {

		    
			let bannerBox = $('.idx_banner'),
				banner_img = bannerBox.find('.banner_img'),
				banner_foot = bannerBox.find('#banner_foot'),
				play = bannerBox.find('.banner_btn.play'),
				prev = bannerBox.find('.banner_btn.prev'),
				next = bannerBox.find('.banner_btn.next'),
				pager_1 = bannerBox.find('.swiper-pagination-1 .center'),
				pager = bannerBox.find('.swiper-pagination-2 .center'),
				size = banner_img.find('.swiper-slide').length,
				interval = 4500,
				isPlay = true,
				idx = 0,
				c = 'active';
			pager.append(`<div class="active">0</div><div class="line">/</div><div class="number">${size}</div>`);

			var bannerSwiper = new Swiper(banner_img, {
				speed: 500,
				effect: 'fade',
				fadeEffect: {
					crossFade: true,
				},
				allowTouchMove: false,
				loop: true,
				parallax : true,
				autoplay: {
					delay: interval,
                    pauseOnMouseEnter:true,
                    disableOnInteraction:false,
				},
				pagination: {
					el: '.swiper-pagination-1 .center',
					type: 'bullets',
					renderBullet: function (index, className) {
						return '<span class="' + className + '">' + (index + 1) + '</span>';
					}
				},
				navigation: {
					nextEl: next,
					prevEl: prev,
				},
				on: {
					init() {
						if (this.size <= 990) {
							this.allowTouchMove = true;
						}
						console.log(this)
						banner_img.animate({'opacity': 1}, 500);
						banner_foot.animate({'opacity': 1}, 500);
						idx = this.realIndex;
						pager.children('.' + c).text(idx + 1);
						var left = (1 / size).toFixed(2) * 100;
					},
					slideChangeTransitionStart() {
						isPlay = true;
						idx = this.realIndex;
						pager.children('.' + c).text(this.realIndex + 1);
						pager_1.children('.swiper-pagination-bullet-active').removeClass(c);
					},
					resize() {
						if(this.size <= 990){
							this.allowTouchMove = true;
						} else {
							this.allowTouchMove = false;
						}
					}
				}
			})

		$('.idx_banner video').each(function(index){
			   console.log($(this)[0].paused)
			})
			
			
			play.on('click', function() {
				if (isPlay) {
					isPlay = false;
					bannerSwiper.autoplay.stop();
					$(this).addClass(c);
					pager_1.children('.swiper-pagination-bullet-active').addClass(c);
				} else {
					isPlay = true;
					bannerSwiper.autoplay.start();
					$(this).removeClass(c);
					pager_1.children('.swiper-pagination-bullet-active').removeClass(c);
				}
			});
			function btnClick(btn){
				btn.on('click', function() {
					if(play.hasClass(c)){
						isPlay = false;
						bannerSwiper.autoplay.stop();
						pager_1.children('.swiper-pagination-bullet-active').addClass(c);
					}
				});
			}
			btnClick(prev);
			btnClick(next);
		}
		bannerSwiper();
	</script>

	<!-- Service -->

	

	<div class="service_yt0517">
		<div class="service_main w1600">
			<div class="service_list" id="service" style="top: 0px;">
				<ul>
				
									<li class="active">
						<div class="head">
							<div class="icon">
								<i class="iconfont"><img datatype="img" src="/static/picture/icon_ser_1.png" alt=""></i>
								<i class="iconfont active"><img datatype="img" src="/static/picture/icon_ser_1_active.png" alt=""></i>
							</div>
							<div class="order"><v datatype="span">01</v><span datatype="span">. DOWNLOAD</span></div>
						</div>
						<div class="info">
							<div class="title" datatype="span">资料下载中心</div>
							<div class="txt" datatype="span">详尽的产品目录、CAD、使用说明书，软件等供您下载。</div>
						</div>
						<div class="more active hrefall">
							<a href="/download/" class="hrefson">
								<div class="icon"><i></i><i></i></div>
								<div class="txt" datatype="hrefs">了解详情</div>
							</a>
												</div>
					</li>
									<li>
						<div class="head">
							<div class="icon">
								<i class="iconfont"><img datatype="img" src="/static/picture/icon_ser_2.png" alt=""></i>
								<i class="iconfont active"><img datatype="img" src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240103/1-2401031G44RR.png" alt=""></i>
							</div>
							<div class="order"><v datatype="span">02</v><span datatype="span">. PRODUCTS</span></div>
						</div>
						<div class="info">
							<div class="title" datatype="span">产品中心</div>
							<div class="txt" datatype="span">兆众智能一直遵循产品质量是设计进去，制造出来的品质理念；不断钻研专业技术，加快工业自动化的国产化进程。</div>
						</div>
						<div class="more active hrefall">
							<a href="/product/" class="hrefson">
								<div class="icon"><i></i><i></i></div>
								<div class="txt" datatype="hrefs">了解详情</div>
							</a>
						</div>
					</li>
									<li>
						<div class="head">
							<div class="icon">
								<i class="iconfont"><img datatype="img" src="/static/picture/icon_ser_3.png" alt=""></i>
								<i class="iconfont active"><img datatype="img" src="/static/picture/icon_ser_3_active.png" alt=""></i>
							</div>
							<div class="order"><v datatype="span">03</v><span datatype="span">. SOLUTION</span></div>
						</div>
						<div class="info">
							<div class="title" datatype="span">应用案例</div>
							<div class="txt" datatype="span">根据用户需求选择适合您的产品；帮助用户创造价值，用户因为我们而实现更大的梦想。
</div>
						</div>
						<div class="more active hrefall">
							<a href="/solution/" class="hrefson">
								<div class="icon"><i></i><i></i></div>
								<div class="txt" datatype="hrefs">了解详情</div>
							</a>
						</div>
					</li>
									<li>
						<div class="head">
							<div class="icon">
								<i class="iconfont"><img datatype="img" src="/static/picture/icon_ser_4.png" alt=""></i>
								<i class="iconfont active"><img datatype="img" src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240103/1-2401031G40W93.png" alt=""></i>
							</div>
							<div class="order"><v datatype="span">04</v><span datatype="span">. </span></div>
						</div>
						<div class="info">
							<div class="title" datatype="span">免费试用</div>
							<div class="txt" datatype="span">点击这里，免费申请测试机。</div>
						</div>
						<div class="more active hrefall">
							<a href="/mianfeishiyong/" class="hrefson">
								<div class="icon"><i></i><i></i></div>
								<div class="txt" datatype="hrefs">了解详情</div>
							</a>
						</div>
					</li>
									
				</ul>
			</div>
			<div class="cle"></div>
		</div>
	</div>

	<script type="text/javascript">
		function serviceTop() {
			var banner_foot = $('#banner_foot'),
				service = $('#service');
			service.children().children().mouseover(function() {
				$(this).addClass('active').siblings().removeClass('active');

			});
			function moveTop(){
				var b_bottom = parseInt(banner_foot.css('bottom')),
					service_h = service.outerHeight(),
					offsetTop = service.offset().top,
					bottom = parseInt(service.css('padding-bottom')),
					top = $(document).scrollTop();
				if(top > 150){
					banner_foot.css({'bottom': `${bottom + 30}px` });
					service.css({'padding-top': 0,'top': `-${bottom }px`});
				} else {
					banner_foot.css({'bottom': '' });
					service.css({'padding-top': '','top': 0});
				}

			}
			moveTop();
			$(document).scroll(() => moveTop() );
		}
		$(document).ready(() => serviceTop() );
	</script>



	<!-- 产品中心 -->

	<div class="idx_cpzx_yt0517">

		<div class="cpzx_main w1600">

			<div class="idx_title wow fadeInUp" style="visibility: hidden; animation-name: inherit;">
               				<div class="info">
					<div class="title"><span>产品中心</span></div>
					<div class="slogan">
						<p>我们<em>不断</em><em>钻研</em>专业技术<em>，</em><em>加快</em><em>工业自动化</em>的国产化进程，民族品牌未来可期！</p>					</div>
				</div>
              
				<div class="idx_more">

					<a href="/product/">

						<div class="word splt">
                          <span>探索更多产品</span>
						</div>

						<div class="icon"><img src="/static/picture/jt.png" alt="产品中心"></div>

					</a>

				</div>
             			</div>

            <div class="product_search  wow fadeInUp" style="visibility: hidden; animation-name: inherit;">
            	<div class="search_box">
    <form method="get" action="/search.html">
        <input type="hidden" name="method" value="1" style="">            			<div class="icon"><i class="iconfont icon_sousuo"></i></div>
            			<div class="input"><input type="text" placeholder="型号搜索 使用说明" name="keywords" id="keys" class="ipts3" style=""></div>
    </form>
            	</div>
            </div>

			<div class="cpzx_list">

				<ul>

					

										<li class="wow fadeInUp" style="visibility: hidden; animation-name: inherit;">

						<div class="head">

							<div class="img"><div class="center"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240228/1-24022Q0033aG.png" alt="至璨 · CPU单元"></div></div>

							<div class="subtitle">

								<div class="word">CPU UNIT</div>

								<div class="number">/01</div>

							</div>

							<div class="title">至璨 · CPU单元</div>

							<div class="icon"><div class="iconfont icon_arrow-down"></div></div>

						</div>

						<div class="img"><div class="center"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240228/1-24022Q0033aG.png" alt="至璨 · CPU单元"></div></div>

						<div class="menu">

							<div class="center">

								<div class="items">
                              
																		<div class="item"><a href="/cpudanyuan/" title="CPU单元">CPU单元</a></div>
                                    									
								</div>

							</div>

						</div>

						<div class="more"><a href="/productlist1/">Learn More</a></div>

					</li>
                 					<li class="wow fadeInUp" style="visibility: hidden; animation-name: inherit;">

						<div class="head">

							<div class="img"><div class="center"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240228/1-24022Q02R1310.png" alt="至璨 · 总线耦合器"></div></div>

							<div class="subtitle">

								<div class="word">BUS COUPLER</div>

								<div class="number">/02</div>

							</div>

							<div class="title">至璨 · 总线耦合器</div>

							<div class="icon"><div class="iconfont icon_arrow-down"></div></div>

						</div>

						<div class="img"><div class="center"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240228/1-24022Q02R1310.png" alt="至璨 · 总线耦合器"></div></div>

						<div class="menu">

							<div class="center">

								<div class="items">
                              
																		<div class="item"><a href="/nx-dio16n-cclouheqi/" title="NX-DIO16N-CCL耦合器">NX-DIO16N-CCL耦合器</a></div>
                                    									<div class="item"><a href="/nx-dio16n-ectouheqi/" title="NX-DIO16N-ECT耦合器">NX-DIO16N-ECT耦合器</a></div>
                                    									<div class="item"><a href="/nx-dio16n-eipouheqi/" title="NX-DIO16N-EIP耦合器">NX-DIO16N-EIP耦合器</a></div>
                                    									<div class="item"><a href="/nx-dio16p-pntouheqi/" title="NX-DIO16P-PNT耦合器">NX-DIO16P-PNT耦合器</a></div>
                                    									<div class="item"><a href="/NX-ID32N-TCPouheqi/" title="NX-ID32N-TCP耦合器">NX-ID32N-TCP耦合器</a></div>
                                    									<div class="item"><a href="/nx6-1261-rtuouheqi/" title="NX6-1261-RTU耦合器">NX6-1261-RTU耦合器</a></div>
                                    									<div class="item"><a href="/nx6-1277-ect2ouheqi/" title="NX6-1277-ECT2耦合器">NX6-1277-ECT2耦合器</a></div>
                                    									<div class="item"><a href="/nx6-1277-eip2ouheqi/" title="NX6-1277-EIP2耦合器">NX6-1277-EIP2耦合器</a></div>
                                    									<div class="item"><a href="/nx6-1277-pnt2ouheqi/" title="NX6-1277-PNT2耦合器">NX6-1277-PNT2耦合器</a></div>
                                    									
								</div>

							</div>

						</div>

						<div class="more"><a href="/productlist2/">Learn More</a></div>

					</li>
                 					<li class="wow fadeInUp" style="visibility: hidden; animation-name: inherit;">

						<div class="head">

							<div class="img"><div class="center"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240327/1-24032GH12O61.png" alt="至璨 · IO模块"></div></div>

							<div class="subtitle">

								<div class="word">IO MODULE</div>

								<div class="number">/03</div>

							</div>

							<div class="title">至璨 · IO模块</div>

							<div class="icon"><div class="iconfont icon_arrow-down"></div></div>

						</div>

						<div class="img"><div class="center"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240327/1-24032GH12O61.png" alt="至璨 · IO模块"></div></div>

						<div class="menu">

							<div class="center">

								<div class="items">
                              
																		<div class="item"><a href="/productlist3/" title="数字量模块">数字量模块</a></div>
                                    									<div class="item"><a href="/moniliangshurumokuai/" title="模拟量模块">模拟量模块</a></div>
                                    									<div class="item"><a href="/redianzuceliangmokuai/" title="热电阻模块">热电阻模块</a></div>
                                    									<div class="item"><a href="/redianouceliangmokuai/" title="热电偶模块">热电偶模块</a></div>
                                    									<div class="item"><a href="/chuanxingtongxinmokuai/" title="串行通信模块">串行通信模块</a></div>
                                    									<div class="item"><a href="/gaosujishuqimokuai/" title="高速计数器模块">高速计数器模块</a></div>
                                    									<div class="item"><a href="/dianyuanzhongjimokuai/" title="电源中继模块">电源中继模块</a></div>
                                    									<div class="item"><a href="/chuanganqijiexiangongdianmokuai/" title="传感器供电模块">传感器供电模块</a></div>
                                    									
								</div>

							</div>

						</div>

						<div class="more"><a href="/mokuai/">Learn More</a></div>

					</li>
                 					<li class="wow fadeInUp" style="visibility: hidden; animation-name: inherit;">

						<div class="head">

							<div class="img"><div class="center"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20250508/1-25050Q1260U43.png" alt="至璨 · 接近传感器"></div></div>

							<div class="subtitle">

								<div class="word">PROXIMITY SENSOR</div>

								<div class="number">/04</div>

							</div>

							<div class="title">至璨 · 接近传感器</div>

							<div class="icon"><div class="iconfont icon_arrow-down"></div></div>

						</div>

						<div class="img"><div class="center"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20250508/1-25050Q1260U43.png" alt="至璨 · 接近传感器"></div></div>

						<div class="menu">

							<div class="center">

								<div class="items">
                              
																		<div class="item"><a href="/e2fxiliedianganshijiejinchuanganqi/" title="E2F系列电感式接近传感器">E2F系列电感式接近传感器</a></div>
                                    									<div class="item"><a href="/e2bxiliechangjulidianganshijiejinchuanganqi/" title="E2B系列长距离电感式接近传感器">E2B系列长距离电感式接近传感器</a></div>
                                    									<div class="item"><a href="/dianganshijiejinchuanganqi/" title="E2ZE系列电感式接近传感器">E2ZE系列电感式接近传感器</a></div>
                                    									<div class="item"><a href="/e2kxiliedianrongshijiejinchuanganqi/" title="E2K系列电容式接近传感器">E2K系列电容式接近传感器</a></div>
                                    									<div class="item"><a href="/ezexilieweixiaoxingdianganshijiejinchuanganqi/" title="EZE系列微小型电感式接近传感器">EZE系列微小型电感式接近传感器</a></div>
                                    									<div class="item"><a href="/tlxiliefangxingjiejinchuanganqi/" title="TL系列方形接近传感器">TL系列方形接近传感器</a></div>
                                    									<div class="item"><a href="/e2cedianganshijiejinchuanganqi/" title="E2CE系列电感式接近传感器">E2CE系列电感式接近传感器</a></div>
                                    									<div class="item"><a href="/xs-fjiejinchuanganqifangshuilianjieqihangchatou/" title="XS□F接近传感器防水连接器航插头">XS□F接近传感器防水连接器航插头</a></div>
                                    									
								</div>

							</div>

						</div>

						<div class="more"><a href="/jiejinchuanganqi/">Learn More</a></div>

					</li>
                 					<li class="wow fadeInUp" style="visibility: hidden; animation-name: inherit;">

						<div class="head">

							<div class="img"><div class="center"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20250508/1-25050Q34101640.png" alt="至璨 · 光电传感器"></div></div>

							<div class="subtitle">

								<div class="word">PHOTOELECTRIC SENSORS</div>

								<div class="number">/05</div>

							</div>

							<div class="title">至璨 · 光电传感器</div>

							<div class="icon"><div class="iconfont icon_arrow-down"></div></div>

						</div>

						<div class="img"><div class="center"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20250508/1-25050Q34101640.png" alt="至璨 · 光电传感器"></div></div>

						<div class="menu">

							<div class="center">

								<div class="items">
                              
																		<div class="item"><a href="/e3zcbeijingyizhixingguangdianchuanganqi/" title="E3ZC系列背景抑制光电传感器">E3ZC系列背景抑制光电传感器</a></div>
                                    									<div class="item"><a href="/e3zcxilieyuanjulixingguangdianchuanganqi/" title="E3ZC系列远距离型光电传感器">E3ZC系列远距离型光电传感器</a></div>
                                    									<div class="item"><a href="/e3txiliechaoboxingguangdiankaiguanchuanganqi/" title="E3T系列超薄型光电开关传感器">E3T系列超薄型光电开关传感器</a></div>
                                    									<div class="item"><a href="/e3jkxiliefangxing-da-guangdianchuanganqi/" title="E3JK系列方形(大)光电传感器">E3JK系列方形(大)光电传感器</a></div>
                                    									<div class="item"><a href="/e3zcxiliehongseguang-xiao-guangdianchuanganqi/" title="E3ZC系列红色光(小)光电传感器">E3ZC系列红色光(小)光电传感器</a></div>
                                    									<div class="item"><a href="/eexilieaocaoxingguangdiankaiguanchuanganqi/" title="EE系列凹槽型光电开关传感器">EE系列凹槽型光电开关传感器</a></div>
                                    									<div class="item"><a href="/e2exilieaocaoxingguangdiankaiguanchuanganqi/" title="E2E系列凹槽型光电开关传感器">E2E系列凹槽型光电开关传感器</a></div>
                                    									<div class="item"><a href="/e3faxilieyuanzhuxinghongwaiguangguangdianchuanganqi/" title="E3FA系列红外光光电传感器">E3FA系列红外光光电传感器</a></div>
                                    									<div class="item"><a href="/e3raxilieyuanzhuxingguangdianchuanganqi/" title="E3RA系列圆柱型光电传感器">E3RA系列圆柱型光电传感器</a></div>
                                    									<div class="item"><a href="/e3faxiliejingjixingyuanzhuguangdianchuanganqi/" title="E3FA系列经济型圆柱光电传感器">E3FA系列经济型圆柱光电传感器</a></div>
                                    									
								</div>

							</div>

						</div>

						<div class="more"><a href="/guangdianchuanganqi/">Learn More</a></div>

					</li>
                 					<li class="wow fadeInUp" style="visibility: hidden; animation-name: inherit;">

						<div class="head">

							<div class="img"><div class="center"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20250508/1-25050Q34Z0540.png" alt="至璨 · 继电器"></div></div>

							<div class="subtitle">

								<div class="word">RELAY</div>

								<div class="number">/06</div>

							</div>

							<div class="title">至璨 · 继电器</div>

							<div class="icon"><div class="iconfont icon_arrow-down"></div></div>

						</div>

						<div class="img"><div class="center"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20250508/1-25050Q34Z0540.png" alt="至璨 · 继电器"></div></div>

						<div class="menu">

							<div class="center">

								<div class="items">
                              
																		<div class="item"><a href="/zy-hyxiliezhongjianjidianqi/" title="ZY＆HY系列中间继电器">ZY＆HY系列中间继电器</a></div>
                                    									<div class="item"><a href="/g3n-xiliegutaijidianqi/" title="G3N□系列固态继电器">G3N□系列固态继电器</a></div>
                                    									
								</div>

							</div>

						</div>

						<div class="more"><a href="/-chong--lu-juan----hong--hu-dian-/">Learn More</a></div>

					</li>
                 					<li class="wow fadeInUp" style="visibility: hidden; animation-name: inherit;">

						<div class="head">

							<div class="img"><div class="center"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240618/1-24061P9252E30.png" alt="至璨 · 激光光电传感器"></div></div>

							<div class="subtitle">

								<div class="word">LASER PHOTOELECTRIC SENSOR</div>

								<div class="number">/07</div>

							</div>

							<div class="title">至璨 · 激光光电传感器</div>

							<div class="icon"><div class="iconfont icon_arrow-down"></div></div>

						</div>

						<div class="img"><div class="center"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240618/1-24061P9252E30.png" alt="至璨 · 激光光电传感器"></div></div>

						<div class="menu">

							<div class="center">

								<div class="items">
                              
																		<div class="item"><a href="/e3fayuanzhuxingjiguangguangdianchuanganqi/" title="E3FA系列激光光电传感器">E3FA系列激光光电传感器</a></div>
                                    									<div class="item"><a href="/e3zcfangxingjiguangguangdianchuanganqi/" title="E3ZC系列方形激光光电传感器">E3ZC系列方形激光光电传感器</a></div>
                                    									<div class="item"><a href="/e3zkfangxingjiguangguangdianchuanganqi/" title="E3ZK系列方形激光光电传感器">E3ZK系列方形激光光电传感器</a></div>
                                    									
								</div>

							</div>

						</div>

						<div class="more"><a href="/jiguangguangdianchuanganqi/">Learn More</a></div>

					</li>
                 					<li class="wow fadeInUp" style="visibility: hidden; animation-name: inherit;">

						<div class="head">

							<div class="img"><div class="center"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240605/1-240605152352X2.png" alt="至璨 · 激光位移传感器"></div></div>

							<div class="subtitle">

								<div class="word">LASER DISPLACEMENT SENSOR</div>

								<div class="number">/08</div>

							</div>

							<div class="title">至璨 · 激光位移传感器</div>

							<div class="icon"><div class="iconfont icon_arrow-down"></div></div>

						</div>

						<div class="img"><div class="center"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240605/1-240605152352X2.png" alt="至璨 · 激光位移传感器"></div></div>

						<div class="menu">

							<div class="center">

								<div class="items">
                              
																		<div class="item"><a href="/e3cxxiliejiguangweiyichuanganqi/" title="E3CX系列激光位移传感器">E3CX系列激光位移传感器</a></div>
                                    									
								</div>

							</div>

						</div>

						<div class="more"><a href="/jiguangweiyichuanganqi/">Learn More</a></div>

					</li>
                 					<li class="wow fadeInUp" style="visibility: hidden; animation-name: inherit;">

						<div class="head">

							<div class="img"><div class="center"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20241114/1-24111409335QA.png" alt="至璨 · 色标传感器"></div></div>

							<div class="subtitle">

								<div class="word">COLOR MARK SENSOR</div>

								<div class="number">/09</div>

							</div>

							<div class="title">至璨 · 色标传感器</div>

							<div class="icon"><div class="iconfont icon_arrow-down"></div></div>

						</div>

						<div class="img"><div class="center"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20241114/1-24111409335QA.png" alt="至璨 · 色标传感器"></div></div>

						<div class="menu">

							<div class="center">

								<div class="items">
                              
																		<div class="item"><a href="/e3nxxiliesebiaochuanganqi/" title="E3NX系列色标传感器">E3NX系列色标传感器</a></div>
                                    									<div class="item"><a href="/e3nxxiliebaiseguangsebiaochuanganqi/" title=" E3NX系列白色光色标传感器"> E3NX系列白色光色标传感器</a></div>
                                    									<div class="item"><a href="/e3nxxiliefentishibaiseguangsebiaochuanganqi/" title="E3NX系列分体式白色光色标传感器">E3NX系列分体式白色光色标传感器</a></div>
                                    									
								</div>

							</div>

						</div>

						<div class="more"><a href="/sebiaochuanganqi/">Learn More</a></div>

					</li>
                 					<li class="wow fadeInUp" style="visibility: hidden; animation-name: inherit;">

						<div class="head">

							<div class="img"><div class="center"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240703/1-240F3094145353.png" alt="至璨 · 光纤传感器"></div></div>

							<div class="subtitle">

								<div class="word">FIBER OPTIC SENSORS</div>

								<div class="number">/010</div>

							</div>

							<div class="title">至璨 · 光纤传感器</div>

							<div class="icon"><div class="iconfont icon_arrow-down"></div></div>

						</div>

						<div class="img"><div class="center"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240703/1-240F3094145353.png" alt="至璨 · 光纤传感器"></div></div>

						<div class="menu">

							<div class="center">

								<div class="items">
                              
																		<div class="item"><a href="/e3hx-hdxilieshuziguangxianchuanganqi/" title="E3HX-HD系列数字光纤传感器">E3HX-HD系列数字光纤传感器</a></div>
                                    									<div class="item"><a href="/e3hxxilieshuziguangxianchuanganqi/" title="E3HX-CD系列数字光纤传感器">E3HX-CD系列数字光纤传感器</a></div>
                                    									<div class="item"><a href="/e3hx-zdxilieshuziguangxianchuanganqi/" title="E3HX-ZD系列数字光纤传感器">E3HX-ZD系列数字光纤传感器</a></div>
                                    									<div class="item"><a href="/e3hx-naxilieshuziguangxianchuanganqi/" title="E3HX-NA系列数字光纤传感器">E3HX-NA系列数字光纤传感器</a></div>
                                    									
								</div>

							</div>

						</div>

						<div class="more"><a href="/guangxianchuanganqi/">Learn More</a></div>

					</li>
                 					<li class="wow fadeInUp" style="visibility: hidden; animation-name: inherit;">

						<div class="head">

							<div class="img"><div class="center"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20250508/1-25050Q4150M56.png" alt="至璨 · 微动/行程/限位开关"></div></div>

							<div class="subtitle">

								<div class="word">MICRO/LIMIT SWITCH</div>

								<div class="number">/011</div>

							</div>

							<div class="title">至璨 · 微动/行程/限位开关</div>

							<div class="icon"><div class="iconfont icon_arrow-down"></div></div>

						</div>

						<div class="img"><div class="center"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20250508/1-25050Q4150M56.png" alt="至璨 · 微动/行程/限位开关"></div></div>

						<div class="menu">

							<div class="center">

								<div class="items">
                              
																		<div class="item"><a href="/zd4vxiliexianweikaiguan/" title="ZD4V系列限位开关">ZD4V系列限位开关</a></div>
                                    									<div class="item"><a href="/zc-d4mcxiliefengzhuangxianweikaiguan/" title="ZC-D4MC系列封装限位开关">ZC-D4MC系列封装限位开关</a></div>
                                    									<div class="item"><a href="/zc-d2vwxiliexiaoxingfangshuiweidongkaiguan/" title="ZC-D2VW系列小型防水微动开关">ZC-D2VW系列小型防水微动开关</a></div>
                                    									<div class="item"><a href="/zc-15xilieweidongkaiguan/" title="ZC-15系列微动开关">ZC-15系列微动开关</a></div>
                                    									
								</div>

							</div>

						</div>

						<div class="more"><a href="/-chong--lu-huan--liao---/">Learn More</a></div>

					</li>
                 					<li class="wow fadeInUp" style="visibility: hidden; animation-name: inherit;">

						<div class="head">

							<div class="img"><div class="center"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20241104/1-241104133FW37.png" alt="至璨 · 管道液位传感器"></div></div>

							<div class="subtitle">

								<div class="word">PIPELINE LEVEL SENSOR</div>

								<div class="number">/012</div>

							</div>

							<div class="title">至璨 · 管道液位传感器</div>

							<div class="icon"><div class="iconfont icon_arrow-down"></div></div>

						</div>

						<div class="img"><div class="center"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20241104/1-241104133FW37.png" alt="至璨 · 管道液位传感器"></div></div>

						<div class="menu">

							<div class="center">

								<div class="items">
                              
																		<div class="item"><a href="/e2kxilieguandaoshiyeweichuanganqi/" title="E2K系列管道式液位传感器">E2K系列管道式液位传感器</a></div>
                                    									
								</div>

							</div>

						</div>

						<div class="more"><a href="/-chong--lu-bing--liao-donghuan-----/">Learn More</a></div>

					</li>
                 
					
				</ul>

			</div>

		</div>

	</div>

	<script type="text/javascript">

		function cpzxM(){
			var body_w = $(window).width();
			if(body_w <= 990){
				var box = $('.cpzx_list'),
					ul = box.children(),
					li = ul.children(),
					c = 'active';
				li.each(function(){
					$(this).children('.head').on('click', function(event){
						if($(this).siblings('.menu').is(':hidden')){
							$(this).siblings('.menu').slideDown(300).parent().siblings().removeClass(c).children('.menu').slideUp(300);
							$(this).parent().addClass(c);
						} else {
							$(this).parent().removeClass(c);
							$(this).siblings('.menu').slideUp(300);
						}
					});
				});
			}
		}
		cpzxM();
	</script>



	<!-- 应用案例 -->

	<div class="idx_cases_yt0519 idx_cases_yt05191">

		<div class="cases_main w1600">

			<div class="idx_title">

				<div class="info">

					<div class="title"><span>检测实验应用教学</span></div>
                     					<div class="slogan">
                        <p><span style="color: rgb(191, 191, 191);">至璨 · ZHICAN——</span>工业品检测试验是确保产品质量、性能和安全性的重要环节</p>					</div>
                   				</div>

				<div class="idx_more">
					<a href="/cases2/">
						<div class="word">
                             <span> 查看全部案例</span>
						</div>
						<div class="icon"><img src="/static/picture/jt.png" alt="检测实验应用教学"></div>
					</a>
				</div>
			</div>

			<div class="cases_content">
				<div class="swiper_box">
					<div class="swiper_images wow fadeInUp" style="visibility: hidden; animation-name: inherit;">
						<div class="center">
							<div class="ab">
								<div class="swiper_img">
									<div class="swiper-wrapper">
							            									    <video id="video" src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240511/1-240511105945W6.mp4" poster="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240511/1-2405110940513K.jpg" controls=""></video>
							            									</div>
								
								</div>
							</div>
						</div>
					</div>

					<div class="content">
						<div class="move"></div>
						<div class="infos wow fadeInUp" style="visibility: hidden; animation-name: inherit;">

                                    <div class="swiper swiper-container-initialized swiper-container-horizontal">
                                        <div class="swiper-wrapper" style="transition-duration: 0ms; transform: translate3d(-489px, 0px, 0px);"><div class="swiper-slide swiper-slide-duplicate swiper-slide-prev" data-src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240812/1-240Q216221K91.jpg" data-url="//zhaozhongai.oss-cn-shanghai.aliyuncs.com/uploads/media/20250115/E3NX%E5%BD%A9%E8%89%B2%E6%A8%A1%E5%BC%8F%E8%AE%BE%E5%AE%9A.mp4" data-swiper-slide-index="22" style="width: 489px;">								
                                                                    <div class="title">至璨色标传感器——E3NX系列彩色模式设定</div>
                                    								<div class="textarea">兆众智能“至璨 · ZHICAN”品牌旗舰系列产品涵盖：高性能PLC、分布式I/O模块、CPU单元模块、接近传感器、光电传感器、继电器、光纤传感器、色标传感器、激光位移传感器、开关、超声波传感器、视觉传感器、安全产品、工业无线、物联网网关、工业交换机、协议转换器等以及相关的智慧化工业解决方案，广泛应用于智能制造、工业自...</div>
                                    								<div class="more active">
                                    									<a href="javascript:;" data-url="//zhaozhongai.oss-cn-shanghai.aliyuncs.com/uploads/media/20250115/E3NX%E5%BD%A9%E8%89%B2%E6%A8%A1%E5%BC%8F%E8%AE%BE%E5%AE%9A.mp4">
                                    										<div class="icon"><i></i><i></i></div>
                                    										<div class="txt">点击播放</div>
                                    									</a>
                                    								</div></div>
                                            	                                                 <div class="swiper-slide swiper-slide-active" data-src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240511/1-2405110940513K.jpg" data-url="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240511/1-240511105945W6.mp4" data-swiper-slide-index="0" style="width: 489px;">								
                                                                    <div class="title">至璨 · ZHICAN——E2ZE系列IP68防水接近传感器检测实验</div>
                                    								<div class="textarea">兆众智能“至璨 · ZHICAN”品牌旗舰系列产品涵盖：高性能PLC、分布式I/O模块、CPU单元模块、接近传感器、光电传感器、继电器、光纤传感器、色标传感器、激光位移传感器、开关、超声波传感器、视觉传感器、安全产品、工业无线、物联网网关、工业交换机、协议转换器等以及相关的智慧化工业解决方案，广泛应用于智能制造、工业自...</div>
                                    								<div class="more active">
                                    									<a href="javascript:;" data-url="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240511/1-240511105945W6.mp4">
                                    										<div class="icon"><i></i><i></i></div>
                                    										<div class="txt">点击播放</div>
                                    									</a>
                                    								</div></div>
                                                                                              <div class="swiper-slide swiper-slide-next" data-src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20241115/1-2411151AJL49.jpg" data-url="//zhaozhongai.oss-cn-shanghai.aliyuncs.com/uploads/media/20250115/E2F%E7%B3%BB%E5%88%97%E6%97%A0%E8%A1%B0%E5%87%8F%E6%8E%A5%E8%BF%91%E4%BC%A0%E6%84%9F%E5%99%A8%E5%AE%9E%E6%B5%8B%E8%A7%86%E9%A2%91.mp4" data-swiper-slide-index="1" style="width: 489px;">								
                                                                    <div class="title">至璨 · ZHICAN——E2F系列无衰减接近传感器检测实验</div>
                                    								<div class="textarea">兆众智能“至璨 · ZHICAN”品牌旗舰系列产品涵盖：高性能PLC、分布式I/O模块、CPU单元模块、接近传感器、光电传感器、继电器、光纤传感器、色标传感器、激光位移传感器、开关、超声波传感器、视觉传感器、安全产品、工业无线、物联网网关、工业交换机、协议转换器等以及相关的智慧化工业解决方案，广泛应用于智能制造、工业自...</div>
                                    								<div class="more active">
                                    									<a href="javascript:;" data-url="//zhaozhongai.oss-cn-shanghai.aliyuncs.com/uploads/media/20250115/E2F%E7%B3%BB%E5%88%97%E6%97%A0%E8%A1%B0%E5%87%8F%E6%8E%A5%E8%BF%91%E4%BC%A0%E6%84%9F%E5%99%A8%E5%AE%9E%E6%B5%8B%E8%A7%86%E9%A2%91.mp4">
                                    										<div class="icon"><i></i><i></i></div>
                                    										<div class="txt">点击播放</div>
                                    									</a>
                                    								</div></div>
                                                                                              <div class="swiper-slide" data-src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240511/1-24051109521XC.jpg" data-url="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240710/1-240G0143S0Y0.mp4" data-swiper-slide-index="2" style="width: 489px;">								
                                                                    <div class="title">至璨 · ZHICAN——E3ZC系列背景抑制光电传感器检测实验</div>
                                    								<div class="textarea">兆众智能“至璨 · ZHICAN”品牌旗舰系列产品涵盖：高性能PLC、分布式I/O模块、CPU单元模块、接近传感器、光电传感器、继电器、光纤传感器、色标传感器、激光位移传感器、开关、超声波传感器、视觉传感器、安全产品、工业无线、物联网网关、工业交换机、协议转换器等以及相关的智慧化工业解决方案，广泛应用于智能制造、工业自...</div>
                                    								<div class="more active">
                                    									<a href="javascript:;" data-url="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240710/1-240G0143S0Y0.mp4">
                                    										<div class="icon"><i></i><i></i></div>
                                    										<div class="txt">点击播放</div>
                                    									</a>
                                    								</div></div>
                                                                                              <div class="swiper-slide" data-src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20250113/1-250113141T4114.jpg" data-url="//zhaozhongai.oss-cn-shanghai.aliyuncs.com/uploads/media/20250115/EE%EF%BC%86E2E%E5%87%B9%E6%A7%BD%E5%9E%8B%E5%85%89%E7%94%B5%E5%BC%80%E5%85%B3%E4%BC%A0%E6%84%9F%E5%99%A8%E6%A3%80%E6%B5%8B%E5%AE%9E%E9%AA%8C.mp4" data-swiper-slide-index="3" style="width: 489px;">								
                                                                    <div class="title">至璨 · ZHICAN——EE＆E2E凹槽型光电开关传感器检测实验</div>
                                    								<div class="textarea">兆众智能“至璨 · ZHICAN”品牌旗舰系列产品涵盖：高性能PLC、分布式I/O模块、CPU单元模块、接近传感器、光电传感器、继电器、光纤传感器、色标传感器、激光位移传感器、开关、超声波传感器、视觉传感器、安全产品、工业无线、物联网网关、工业交换机、协议转换器等以及相关的智慧化工业解决方案，广泛应用于智能制造、工业自...</div>
                                    								<div class="more active">
                                    									<a href="javascript:;" data-url="//zhaozhongai.oss-cn-shanghai.aliyuncs.com/uploads/media/20250115/EE%EF%BC%86E2E%E5%87%B9%E6%A7%BD%E5%9E%8B%E5%85%89%E7%94%B5%E5%BC%80%E5%85%B3%E4%BC%A0%E6%84%9F%E5%99%A8%E6%A3%80%E6%B5%8B%E5%AE%9E%E9%AA%8C.mp4">
                                    										<div class="icon"><i></i><i></i></div>
                                    										<div class="txt">点击播放</div>
                                    									</a>
                                    								</div></div>
                                                                                              <div class="swiper-slide" data-src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20250113/1-250113140U1628.jpg" data-url="//zhaozhongai.oss-cn-shanghai.aliyuncs.com/uploads/media/20250115/E3RA%E7%B3%BB%E5%88%97%E7%BA%A2%E5%A4%96%E5%85%89%E5%85%89%E7%94%B5%E4%BC%A0%E6%84%9F%E5%99%A8%E6%A3%80%E6%B5%8B%E5%AE%9E%E9%AA%8C.mp4" data-swiper-slide-index="4" style="width: 489px;">								
                                                                    <div class="title">至璨 · ZHICAN——E3RA系列红外光光电传感器检测实验</div>
                                    								<div class="textarea">兆众智能“至璨 · ZHICAN”品牌旗舰系列产品涵盖：高性能PLC、分布式I/O模块、CPU单元模块、接近传感器、光电传感器、继电器、光纤传感器、色标传感器、激光位移传感器、开关、超声波传感器、视觉传感器、安全产品、工业无线、物联网网关、工业交换机、协议转换器等以及相关的智慧化工业解决方案，广泛应用于智能制造、工业自...</div>
                                    								<div class="more active">
                                    									<a href="javascript:;" data-url="//zhaozhongai.oss-cn-shanghai.aliyuncs.com/uploads/media/20250115/E3RA%E7%B3%BB%E5%88%97%E7%BA%A2%E5%A4%96%E5%85%89%E5%85%89%E7%94%B5%E4%BC%A0%E6%84%9F%E5%99%A8%E6%A3%80%E6%B5%8B%E5%AE%9E%E9%AA%8C.mp4">
                                    										<div class="icon"><i></i><i></i></div>
                                    										<div class="txt">点击播放</div>
                                    									</a>
                                    								</div></div>
                                                                                              <div class="swiper-slide" data-src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240516/1-2405161K14LY.jpg" data-url="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240516/1-2405161K324919.mp4" data-swiper-slide-index="5" style="width: 489px;">								
                                                                    <div class="title">至璨 · ZHICAN——E3ZK系列方形激光光电传感器检测实验</div>
                                    								<div class="textarea">兆众智能“至璨 · ZHICAN”品牌旗舰系列产品涵盖：高性能PLC、分布式I/O模块、CPU单元模块、接近传感器、光电传感器、继电器、光纤传感器、色标传感器、激光位移传感器、开关、超声波传感器、视觉传感器、安全产品、工业无线、物联网网关、工业交换机、协议转换器等以及相关的智慧化工业解决方案，广泛应用于智能制造、工业自...</div>
                                    								<div class="more active">
                                    									<a href="javascript:;" data-url="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240516/1-2405161K324919.mp4">
                                    										<div class="icon"><i></i><i></i></div>
                                    										<div class="txt">点击播放</div>
                                    									</a>
                                    								</div></div>
                                                                                              <div class="swiper-slide" data-src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20250113/1-250113140144915.jpg" data-url="//zhaozhongai.oss-cn-shanghai.aliyuncs.com/uploads/media/20250115/E2K%E7%AE%A1%E9%81%93%E5%BC%8F%E6%B6%B2%E4%BD%8D%E4%BC%A0%E6%84%9F%E5%99%A8%E5%AE%9E%E6%B5%8B.mp4" data-swiper-slide-index="6" style="width: 489px;">								
                                                                    <div class="title">至璨 · ZHICAN——E2K系列管道式液位传感器检测实验</div>
                                    								<div class="textarea">兆众智能“至璨 · ZHICAN”品牌旗舰系列产品涵盖：高性能PLC、分布式I/O模块、CPU单元模块、接近传感器、光电传感器、继电器、光纤传感器、色标传感器、激光位移传感器、开关、超声波传感器、视觉传感器、安全产品、工业无线、物联网网关、工业交换机、协议转换器等以及相关的智慧化工业解决方案，广泛应用于智能制造、工业自...</div>
                                    								<div class="more active">
                                    									<a href="javascript:;" data-url="//zhaozhongai.oss-cn-shanghai.aliyuncs.com/uploads/media/20250115/E2K%E7%AE%A1%E9%81%93%E5%BC%8F%E6%B6%B2%E4%BD%8D%E4%BC%A0%E6%84%9F%E5%99%A8%E5%AE%9E%E6%B5%8B.mp4">
                                    										<div class="icon"><i></i><i></i></div>
                                    										<div class="txt">点击播放</div>
                                    									</a>
                                    								</div></div>
                                                                                              <div class="swiper-slide" data-src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240710/1-240G0144311236.jpg" data-url="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240710/1-240G0144459105.mp4" data-swiper-slide-index="7" style="width: 489px;">								
                                                                    <div class="title">至璨 · ZHICAN——E3FA系列防水激光光电传感器检测实验</div>
                                    								<div class="textarea">兆众智能“至璨 · ZHICAN”品牌旗舰系列产品涵盖：高性能PLC、分布式I/O模块、CPU单元模块、接近传感器、光电传感器、继电器、光纤传感器、色标传感器、激光位移传感器、开关、超声波传感器、视觉传感器、安全产品、工业无线、物联网网关、工业交换机、协议转换器等以及相关的智慧化工业解决方案，广泛应用于智能制造、工业自...</div>
                                    								<div class="more active">
                                    									<a href="javascript:;" data-url="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240710/1-240G0144459105.mp4">
                                    										<div class="icon"><i></i><i></i></div>
                                    										<div class="txt">点击播放</div>
                                    									</a>
                                    								</div></div>
                                                                                              <div class="swiper-slide" data-src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20241115/1-2411151G30aN.jpg" data-url="//zhaozhongai.oss-cn-shanghai.aliyuncs.com/uploads/media/20250115/E3NX%E8%BF%9C%E8%B7%9D%E7%A6%BB%E8%89%B2%E6%A0%87%E4%BC%A0%E6%84%9F%E5%99%A8.mp4" data-swiper-slide-index="8" style="width: 489px;">								
                                                                    <div class="title">至璨 · ZHICAN——E3NX系列远距离高灵敏度色标传感器检测实验</div>
                                    								<div class="textarea">兆众智能“至璨 · ZHICAN”品牌旗舰系列产品涵盖：高性能PLC、分布式I/O模块、CPU单元模块、接近传感器、光电传感器、继电器、光纤传感器、色标传感器、激光位移传感器、开关、超声波传感器、视觉传感器、安全产品、工业无线、物联网网关、工业交换机、协议转换器等以及相关的智慧化工业解决方案，广泛应用于智能制造、工业自...</div>
                                    								<div class="more active">
                                    									<a href="javascript:;" data-url="//zhaozhongai.oss-cn-shanghai.aliyuncs.com/uploads/media/20250115/E3NX%E8%BF%9C%E8%B7%9D%E7%A6%BB%E8%89%B2%E6%A0%87%E4%BC%A0%E6%84%9F%E5%99%A8.mp4">
                                    										<div class="icon"><i></i><i></i></div>
                                    										<div class="txt">点击播放</div>
                                    									</a>
                                    								</div></div>
                                                                                              <div class="swiper-slide" data-src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240531/1-2405311I555a9.jpg" data-url="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240531/1-2405311IF2360.mp4" data-swiper-slide-index="9" style="width: 489px;">								
                                                                    <div class="title">至璨 · ZHICAN——E3CX系列激光位移传感器检测实验</div>
                                    								<div class="textarea">兆众智能“至璨 · ZHICAN”品牌旗舰系列产品涵盖：高性能PLC、分布式I/O模块、CPU单元模块、接近传感器、光电传感器、继电器、光纤传感器、色标传感器、激光位移传感器、开关、超声波传感器、视觉传感器、安全产品、工业无线、物联网网关、工业交换机、协议转换器等以及相关的智慧化工业解决方案，广泛应用于智能制造、工业自...</div>
                                    								<div class="more active">
                                    									<a href="javascript:;" data-url="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240531/1-2405311IF2360.mp4">
                                    										<div class="icon"><i></i><i></i></div>
                                    										<div class="txt">点击播放</div>
                                    									</a>
                                    								</div></div>
                                                                                              <div class="swiper-slide" data-src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20241115/1-241115163114535.jpg" data-url="//zhaozhongai.oss-cn-shanghai.aliyuncs.com/uploads/media/20250115/E2K%E7%B3%BB%E5%88%973-4%E8%87%B3%E7%92%A8%E7%94%B5%E5%AE%B9%E5%BC%8F%E4%BC%A0%E6%84%9F%E5%99%A8.mp4" data-swiper-slide-index="10" style="width: 489px;">								
                                                                    <div class="title">至璨 · ZHICAN——E2K系列电容式传感器检测实验</div>
                                    								<div class="textarea">兆众智能“至璨 · ZHICAN”品牌旗舰系列产品涵盖：高性能PLC、分布式I/O模块、CPU单元模块、接近传感器、光电传感器、继电器、光纤传感器、色标传感器、激光位移传感器、开关、超声波传感器、视觉传感器、安全产品、工业无线、物联网网关、工业交换机、协议转换器等以及相关的智慧化工业解决方案，广泛应用于智能制造、工业自...</div>
                                    								<div class="more active">
                                    									<a href="javascript:;" data-url="//zhaozhongai.oss-cn-shanghai.aliyuncs.com/uploads/media/20250115/E2K%E7%B3%BB%E5%88%973-4%E8%87%B3%E7%92%A8%E7%94%B5%E5%AE%B9%E5%BC%8F%E4%BC%A0%E6%84%9F%E5%99%A8.mp4">
                                    										<div class="icon"><i></i><i></i></div>
                                    										<div class="txt">点击播放</div>
                                    									</a>
                                    								</div></div>
                                                                                              <div class="swiper-slide" data-src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240710/1-240G0163311S9.jpg" data-url="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240710/1-240G016345J45.mp4" data-swiper-slide-index="11" style="width: 489px;">								
                                                                    <div class="title">至璨 · ZHICAN——E3NX系列色标传感器检测实验</div>
                                    								<div class="textarea">兆众智能“至璨 · ZHICAN”品牌旗舰系列产品涵盖：高性能PLC、分布式I/O模块、CPU单元模块、接近传感器、光电传感器、继电器、光纤传感器、色标传感器、激光位移传感器、开关、超声波传感器、视觉传感器、安全产品、工业无线、物联网网关、工业交换机、协议转换器等以及相关的智慧化工业解决方案，广泛应用于智能制造、工业自...</div>
                                    								<div class="more active">
                                    									<a href="javascript:;" data-url="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240710/1-240G016345J45.mp4">
                                    										<div class="icon"><i></i><i></i></div>
                                    										<div class="txt">点击播放</div>
                                    									</a>
                                    								</div></div>
                                                                                              <div class="swiper-slide" data-src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240710/1-240G01A35I38.jpg" data-url="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240710/1-240G01A60X52.mp4" data-swiper-slide-index="12" style="width: 489px;">								
                                                                    <div class="title">至璨 · ZHICAN——E3HX系列光纤传感器视频检测实验</div>
                                    								<div class="textarea">兆众智能“至璨 · ZHICAN”品牌旗舰系列产品涵盖：高性能PLC、分布式I/O模块、CPU单元模块、接近传感器、光电传感器、继电器、光纤传感器、色标传感器、激光位移传感器、开关、超声波传感器、视觉传感器、安全产品、工业无线、物联网网关、工业交换机、协议转换器等以及相关的智慧化工业解决方案，广泛应用于智能制造、工业自...</div>
                                    								<div class="more active">
                                    									<a href="javascript:;" data-url="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240710/1-240G01A60X52.mp4">
                                    										<div class="icon"><i></i><i></i></div>
                                    										<div class="txt">点击播放</div>
                                    									</a>
                                    								</div></div>
                                                                                              <div class="swiper-slide" data-src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240511/1-240511093421517.jpg" data-url="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240511/1-240511093516112.mp4" data-swiper-slide-index="13" style="width: 489px;">								
                                                                    <div class="title">至璨 · ZHICAN——NX6-1200系列与欧姆龙NX1P2系列走EtherCAT协议应用案例</div>
                                    								<div class="textarea">兆众智能“至璨 · ZHICAN”品牌旗舰系列产品涵盖：高性能PLC、分布式I/O模块、CPU单元模块、接近传感器、光电传感器、继电器、光纤传感器、色标传感器、激光位移传感器、开关、超声波传感器、视觉传感器、安全产品、工业无线、物联网网关、工业交换机、协议转换器等以及相关的智慧化工业解决方案，广泛应用于智能制造、工业自...</div>
                                    								<div class="more active">
                                    									<a href="javascript:;" data-url="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240511/1-240511093516112.mp4">
                                    										<div class="icon"><i></i><i></i></div>
                                    										<div class="txt">点击播放</div>
                                    									</a>
                                    								</div></div>
                                                                                              <div class="swiper-slide" data-src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240511/1-2405110ZS01J.png" data-url="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240511/1-2405110Z91G29.mp4" data-swiper-slide-index="14" style="width: 489px;">								
                                                                    <div class="title">至璨 · ZHICAN——NX6-1200系列与欧姆龙CP2E系列走EtherCAT协议应用案例</div>
                                    								<div class="textarea">兆众智能“至璨 · ZHICAN”品牌旗舰系列产品涵盖：高性能PLC、分布式I/O模块、CPU单元模块、接近传感器、光电传感器、继电器、光纤传感器、色标传感器、激光位移传感器、开关、超声波传感器、视觉传感器、安全产品、工业无线、物联网网关、工业交换机、协议转换器等以及相关的智慧化工业解决方案，广泛应用于智能制造、工业自...</div>
                                    								<div class="more active">
                                    									<a href="javascript:;" data-url="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240511/1-2405110Z91G29.mp4">
                                    										<div class="icon"><i></i><i></i></div>
                                    										<div class="txt">点击播放</div>
                                    									</a>
                                    								</div></div>
                                                                                              <div class="swiper-slide" data-src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240510/1-2405101HH4533.png" data-url="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240430/1-24043015222R19.mp4" data-swiper-slide-index="15" style="width: 489px;">								
                                                                    <div class="title">至璨 · ZHICAN——NX6-1200系列与欧姆龙CP2E系列走Modbus-RTU协议应用案例</div>
                                    								<div class="textarea">兆众智能“至璨 · ZHICAN”品牌旗舰系列产品涵盖：高性能PLC、分布式I/O模块、CPU单元模块、接近传感器、光电传感器、继电器、光纤传感器、色标传感器、激光位移传感器、开关、超声波传感器、视觉传感器、安全产品、工业无线、物联网网关、工业交换机、协议转换器等以及相关的智慧化工业解决方案，广泛应用于智能制造、工业自...</div>
                                    								<div class="more active">
                                    									<a href="javascript:;" data-url="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240430/1-24043015222R19.mp4">
                                    										<div class="icon"><i></i><i></i></div>
                                    										<div class="txt">点击播放</div>
                                    									</a>
                                    								</div></div>
                                                                                              <div class="swiper-slide" data-src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240511/1-240511091542L3.png" data-url="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240511/1-240511091626331.mp4" data-swiper-slide-index="16" style="width: 489px;">								
                                                                    <div class="title">至璨 · ZHICAN——NX6-1200系列与欧姆龙CJ2M系列走EtherNetIP协议应用案例</div>
                                    								<div class="textarea">兆众智能“至璨 · ZHICAN”品牌旗舰系列产品涵盖：高性能PLC、分布式I/O模块、CPU单元模块、接近传感器、光电传感器、继电器、光纤传感器、色标传感器、激光位移传感器、开关、超声波传感器、视觉传感器、安全产品、工业无线、物联网网关、工业交换机、协议转换器等以及相关的智慧化工业解决方案，广泛应用于智能制造、工业自...</div>
                                    								<div class="more active">
                                    									<a href="javascript:;" data-url="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240511/1-240511091626331.mp4">
                                    										<div class="icon"><i></i><i></i></div>
                                    										<div class="txt">点击播放</div>
                                    									</a>
                                    								</div></div>
                                                                                              <div class="swiper-slide" data-src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240511/1-240511092030502.png" data-url="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240511/1-2405110921442H.mp4" data-swiper-slide-index="17" style="width: 489px;">								
                                                                    <div class="title">至璨 · ZHICAN——NX6-1200系列与欧姆龙CP1H系列走Modbus-RTU协议应用案例</div>
                                    								<div class="textarea">兆众智能“至璨 · ZHICAN”品牌旗舰系列产品涵盖：高性能PLC、分布式I/O模块、CPU单元模块、接近传感器、光电传感器、继电器、光纤传感器、色标传感器、激光位移传感器、开关、超声波传感器、视觉传感器、安全产品、工业无线、物联网网关、工业交换机、协议转换器等以及相关的智慧化工业解决方案，广泛应用于智能制造、工业自...</div>
                                    								<div class="more active">
                                    									<a href="javascript:;" data-url="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240511/1-2405110921442H.mp4">
                                    										<div class="icon"><i></i><i></i></div>
                                    										<div class="txt">点击播放</div>
                                    									</a>
                                    								</div></div>
                                                                                              <div class="swiper-slide" data-src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240831/1-240S1101324537.jpg" data-url="//zhaozhongai.oss-cn-shanghai.aliyuncs.com/uploads/media/20250115/%E8%87%B3%E7%92%A8%E8%89%B2%E6%A0%87%E4%BC%A0%E6%84%9F%E5%99%A8%E6%A0%87%E7%AD%BE%E6%A3%80%E6%B5%8B%E4%B8%8D%E8%A7%84%E5%88%99%E5%AD%97%E4%BD%93%E6%88%96%E6%9D%A1%E7%BA%B9%E5%AE%9E%E9%AA%8C%E8%AF%B4%E6%98%8E.mp4" data-swiper-slide-index="18" style="width: 489px;">								
                                                                    <div class="title">至璨色标传感器——标签检测不规则字体或条纹实验说明</div>
                                    								<div class="textarea">兆众智能“至璨 · ZHICAN”品牌旗舰系列产品涵盖：高性能PLC、分布式I/O模块、CPU单元模块、接近传感器、光电传感器、继电器、光纤传感器、色标传感器、激光位移传感器、开关、超声波传感器、视觉传感器、安全产品、工业无线、物联网网关、工业交换机、协议转换器等以及相关的智慧化工业解决方案，广泛应用于智能制造、工业自...</div>
                                    								<div class="more active">
                                    									<a href="javascript:;" data-url="//zhaozhongai.oss-cn-shanghai.aliyuncs.com/uploads/media/20250115/%E8%87%B3%E7%92%A8%E8%89%B2%E6%A0%87%E4%BC%A0%E6%84%9F%E5%99%A8%E6%A0%87%E7%AD%BE%E6%A3%80%E6%B5%8B%E4%B8%8D%E8%A7%84%E5%88%99%E5%AD%97%E4%BD%93%E6%88%96%E6%9D%A1%E7%BA%B9%E5%AE%9E%E9%AA%8C%E8%AF%B4%E6%98%8E.mp4">
                                    										<div class="icon"><i></i><i></i></div>
                                    										<div class="txt">点击播放</div>
                                    									</a>
                                    								</div></div>
                                                                                              <div class="swiper-slide" data-src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240816/1-240Q61Q43E08.jpg" data-url="//zhaozhongai.oss-cn-shanghai.aliyuncs.com/uploads/media/20250115/E3RA%E6%A3%80%E6%B5%8B%E9%80%8F%E6%98%8E%E8%AF%95%E7%AE%A1.mp4" data-swiper-slide-index="19" style="width: 489px;">								
                                                                    <div class="title">至璨光电传感器——E3RA系列检测透明试管实验</div>
                                    								<div class="textarea">兆众智能“至璨 · ZHICAN”品牌旗舰系列产品涵盖：高性能PLC、分布式I/O模块、CPU单元模块、接近传感器、光电传感器、继电器、光纤传感器、色标传感器、激光位移传感器、开关、超声波传感器、视觉传感器、安全产品、工业无线、物联网网关、工业交换机、协议转换器等以及相关的智慧化工业解决方案，广泛应用于智能制造、工业自...</div>
                                    								<div class="more active">
                                    									<a href="javascript:;" data-url="//zhaozhongai.oss-cn-shanghai.aliyuncs.com/uploads/media/20250115/E3RA%E6%A3%80%E6%B5%8B%E9%80%8F%E6%98%8E%E8%AF%95%E7%AE%A1.mp4">
                                    										<div class="icon"><i></i><i></i></div>
                                    										<div class="txt">点击播放</div>
                                    									</a>
                                    								</div></div>
                                                                                              <div class="swiper-slide" data-src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240812/1-240Q2164FBG.jpg" data-url="//zhaozhongai.oss-cn-shanghai.aliyuncs.com/uploads/media/20250115/E3NX%E7%B3%BB%E5%88%97%E8%89%B2%E6%A0%87%E5%B8%B8%E9%97%AD%E6%A8%A1%E5%BC%8F%E8%AE%BE%E5%AE%9A.mp4" data-swiper-slide-index="20" style="width: 489px;">								
                                                                    <div class="title">至璨色标传感器——E3NX系列色标常闭模式设定</div>
                                    								<div class="textarea">兆众智能“至璨 · ZHICAN”品牌旗舰系列产品涵盖：高性能PLC、分布式I/O模块、CPU单元模块、接近传感器、光电传感器、继电器、光纤传感器、色标传感器、激光位移传感器、开关、超声波传感器、视觉传感器、安全产品、工业无线、物联网网关、工业交换机、协议转换器等以及相关的智慧化工业解决方案，广泛应用于智能制造、工业自...</div>
                                    								<div class="more active">
                                    									<a href="javascript:;" data-url="//zhaozhongai.oss-cn-shanghai.aliyuncs.com/uploads/media/20250115/E3NX%E7%B3%BB%E5%88%97%E8%89%B2%E6%A0%87%E5%B8%B8%E9%97%AD%E6%A8%A1%E5%BC%8F%E8%AE%BE%E5%AE%9A.mp4">
                                    										<div class="icon"><i></i><i></i></div>
                                    										<div class="txt">点击播放</div>
                                    									</a>
                                    								</div></div>
                                                                                              <div class="swiper-slide" data-src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240812/1-240Q2163932b8.jpg" data-url="//zhaozhongai.oss-cn-shanghai.aliyuncs.com/uploads/media/20250115/E3NX%E8%89%B2%E6%A0%87%E5%B8%B8%E5%BC%80%E6%A8%A1%E5%BC%8F%E8%AE%BE%E5%AE%9A.mp4" data-swiper-slide-index="21" style="width: 489px;">								
                                                                    <div class="title">至璨色标传感器——E3NX系列色标常开模式设定</div>
                                    								<div class="textarea">兆众智能“至璨 · ZHICAN”品牌旗舰系列产品涵盖：高性能PLC、分布式I/O模块、CPU单元模块、接近传感器、光电传感器、继电器、光纤传感器、色标传感器、激光位移传感器、开关、超声波传感器、视觉传感器、安全产品、工业无线、物联网网关、工业交换机、协议转换器等以及相关的智慧化工业解决方案，广泛应用于智能制造、工业自...</div>
                                    								<div class="more active">
                                    									<a href="javascript:;" data-url="//zhaozhongai.oss-cn-shanghai.aliyuncs.com/uploads/media/20250115/E3NX%E8%89%B2%E6%A0%87%E5%B8%B8%E5%BC%80%E6%A8%A1%E5%BC%8F%E8%AE%BE%E5%AE%9A.mp4">
                                    										<div class="icon"><i></i><i></i></div>
                                    										<div class="txt">点击播放</div>
                                    									</a>
                                    								</div></div>
                                                                                              <div class="swiper-slide swiper-slide-duplicate-prev" data-src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240812/1-240Q216221K91.jpg" data-url="//zhaozhongai.oss-cn-shanghai.aliyuncs.com/uploads/media/20250115/E3NX%E5%BD%A9%E8%89%B2%E6%A8%A1%E5%BC%8F%E8%AE%BE%E5%AE%9A.mp4" data-swiper-slide-index="22" style="width: 489px;">								
                                                                    <div class="title">至璨色标传感器——E3NX系列彩色模式设定</div>
                                    								<div class="textarea">兆众智能“至璨 · ZHICAN”品牌旗舰系列产品涵盖：高性能PLC、分布式I/O模块、CPU单元模块、接近传感器、光电传感器、继电器、光纤传感器、色标传感器、激光位移传感器、开关、超声波传感器、视觉传感器、安全产品、工业无线、物联网网关、工业交换机、协议转换器等以及相关的智慧化工业解决方案，广泛应用于智能制造、工业自...</div>
                                    								<div class="more active">
                                    									<a href="javascript:;" data-url="//zhaozhongai.oss-cn-shanghai.aliyuncs.com/uploads/media/20250115/E3NX%E5%BD%A9%E8%89%B2%E6%A8%A1%E5%BC%8F%E8%AE%BE%E5%AE%9A.mp4">
                                    										<div class="icon"><i></i><i></i></div>
                                    										<div class="txt">点击播放</div>
                                    									</a>
                                    								</div></div>
                                                                                     <div class="swiper-slide swiper-slide-duplicate swiper-slide-duplicate-active" data-src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240511/1-2405110940513K.jpg" data-url="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240511/1-240511105945W6.mp4" data-swiper-slide-index="0" style="width: 489px;">								
                                                                    <div class="title">至璨 · ZHICAN——E2ZE系列IP68防水接近传感器检测实验</div>
                                    								<div class="textarea">兆众智能“至璨 · ZHICAN”品牌旗舰系列产品涵盖：高性能PLC、分布式I/O模块、CPU单元模块、接近传感器、光电传感器、继电器、光纤传感器、色标传感器、激光位移传感器、开关、超声波传感器、视觉传感器、安全产品、工业无线、物联网网关、工业交换机、协议转换器等以及相关的智慧化工业解决方案，广泛应用于智能制造、工业自...</div>
                                    								<div class="more active">
                                    									<a href="javascript:;" data-url="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240511/1-240511105945W6.mp4">
                                    										<div class="icon"><i></i><i></i></div>
                                    										<div class="txt">点击播放</div>
                                    									</a>
                                    								</div></div></div>
                                    
                                        
                                        <!-- 如果需要导航按钮 -->
                                        <div class="swiper-button-prev" tabindex="0" role="button" aria-label="Previous slide"></div>
                                        <div class="swiper-button-next" tabindex="0" role="button" aria-label="Next slide"></div>
                                    
                                    <span class="swiper-notification" aria-live="assertive" aria-atomic="true"></span></div>
						    
						
				
                            
						</div>
					</div>
					<div class="cle"></div>
				</div>
                <style>
                .infos{overflow: hidden;position: relative;}    
                .infos .title{font-size:35px;font-weight: bold;
                }
                .infos .textarea{font-size:14px;line-height:30px;margin:15px 0;color:#999;padding:0 50px;}
                .infos .swiper-button-prev:after{content:"<"}
                .infos .swiper-button-prev,.infos .swiper-button-next{color:#000;top:60%}
                .infos .swiper-button-next:after{content:">"}
                .swiper_img #video{max-width:100%;width: 100%;}
                </style>

			</div>
		</div>

	</div>
 <script>        
  var mySwiper = new Swiper ('.swiper', {
    direction: 'horizontal', // 垂直切换选项
    loop: true, // 循环模式选项
    // 如果需要前进后退按钮
    navigation: {
      nextEl: '.swiper-button-next',
      prevEl: '.swiper-button-prev',
    },
  on: {
    slideChangeTransitionEnd: function(){
            // 获取当前活动的slide的下标
            var activeIndex = this.activeIndex;
            // 获取当前活动的slide的DOM元素
            var activeSlide = this.slides[this.activeIndex];
            // 获取图片链接
            var imageSrc = activeSlide.getAttribute('data-src');
            var url  = activeSlide.getAttribute('data-url');
            // 你可以在这里做你需要的操作，比如显示图片链接
             $('#video').attr('poster',imageSrc);
            $('#video').attr('src',url);
            $("#video")[0].load();
            $("#video")[0].play();         
    },
  },
  }) 
  
  

</script>
 
	<script type="text/javascript">

		$(document).ready(function() {

			function casesSwiper() {
				let casesBox = $('.idx_cases_yt05191'),
					swiper_img = casesBox.find('.swiper_img'),
					swiper_info = casesBox.find('.infos'),
					swiper_tabs = casesBox.find('.swiper_tabs'),
					play = swiper_img.find('.cases_btn.play'),
					prev = swiper_img.find('.cases_btn.prev'),
					next = swiper_img.find('.cases_btn.next'),
					pager_1 = swiper_img.find('.swiper-pagination'),
					pager_2 = swiper_img.find('.swiper-pagination-2 .center'),
					isPlay = true,
					c = 'active';
				pager_1.addClass(c);
				//var size = ${swiper_img.find('.swiper-slide').length};
				var size = swiper_img.find('.swiper-slide').length;
				console.log(swiper_img.find('.swiper-slide'))
			    var html = "<div class='active'>0</div><div class='line'>/</div><div class='number'>"+size+"</div>";
				pager_2.append(html);
				var swiperTabs = new Swiper('.swiper_tabs', {
					slidesPerView: 8,
					watchSlidesVisibility: true,
					allowTouchMove: false,
					breakpoints: {
						768: {
							slidesPerView: 4
						},
						990: {
							slidesPerView: 6
						}
					},
					on: {
						tap(){
							swiper_tabs.find('.item').eq(this.clickedIndex).addClass(c).siblings().removeClass(c);
							tabsSwiper.slideToLoop(this.clickedIndex);
						}
					}
				});
				var tabsSwiper = new Swiper(swiper_img, {
					enabled: true,
					allowTouchMove: false,
					effect: 'fade',
					fadeEffect: {
						crossFade: true,
					},
					autoplay: {
						delay: 4500,
						disableOnInteraction: false,
					},
					pagination: {
						el: '.swiper-pagination',
					},
					navigation: {
						nextEl: next,
						prevEl: prev,
					},
					thumbs: {
						swiper: swiperTabs
					},
					on: {
						init() {
							pager_2.children('.' + c).text(this.realIndex + 1);
							this.allowTouchMove = (window.innerWidth <= 990) ? true : false;
						},
						slideChangeTransitionStart() {
							pager_2.children('.' + c).text(this.realIndex + 1);
							swiper_info.children().eq(this.realIndex).addClass(c).siblings().removeClass(c);
							swiper_tabs.find('.item').eq(this.realIndex).addClass(c).siblings().removeClass(c);
						}
					},
				});
				tabsSwiper.autoplay.stop();
				play.on('click', function() {
					if (isPlay) {
						isPlay = false;
						tabsSwiper.autoplay.stop();
						$(this).addClass(c);
						pager_1.children().addClass(c);
					} else {
						isPlay = true;
						tabsSwiper.autoplay.start();
						$(this).removeClass(c);
						pager_1.children().removeClass(c);
					}

				});

				$(document).scroll(function() {
					if ($(this).scrollTop() > $('.idx_cases_yt05191').offset().top) {
						tabsSwiper.autoplay.start(1000);
						pager_1.removeClass(c);
					}
				});
			}
			casesSwiper();
		});
	</script>





	<!-- 探索行业应用 -->



	<div class="idx_industry_yt0519 " style="background: url(//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240406/1-2404061P02H11.png) no-repeat center;">
		<div class="industry_main w1600 wow fadeInUp" style="visibility: hidden; animation-name: inherit;">
			<div class="idx_title">
				<div class="info">
					<div class="title"><span datatype="span">探索行业应用</span></div>
					<div class="slogan">
                        深耕工控自动化应用行业；<br><span>快速匹配功能需求。</span>
                    </div>
				</div>
			</div>
			<div class="industry_form">
				<div class="form_box">
    <form method="get" action="/search.html">
        <input type="hidden" name="method" value="1" style=""><input type="hidden" name="channelid" id="channelid" value="2" style="">						<div class="content" id="SearchBox">
							<div class="input" id="SearchInput"><input type="text" name="keywords" placeholder="按应用行业" class="hello" style=""></div>
						</div>
						<div class="submit"><input type="submit" value="立即搜索" style=""></div>
    </form>
				</div>
				<div class="form_link">
									<a href="/productlist1/">至璨 · CPU单元</a> 
					<span>｜</span>
										<a href="/productlist2/">至璨 · 总线耦合器</a> 
					<span>｜</span>
										<a href="/mokuai/">至璨 · IO模块</a> 
					<span>｜</span>
										<a href="/jiejinchuanganqi/">至璨 · 接近传感器</a> 
					<span>｜</span>
										<a href="/guangdianchuanganqi/">至璨 · 光电传感器</a> 
					<span>｜</span>
										<a href="/-chong--lu-juan----hong--hu-dian-/">至璨 · 继电器</a> 
					<span>｜</span>
										<a href="/jiguangguangdianchuanganqi/">至璨 · 激光光电传感器</a> 
					<span>｜</span>
										<a href="/jiguangweiyichuanganqi/">至璨 · 激光位移传感器</a> 
					<span>｜</span>
										<a href="/sebiaochuanganqi/">至璨 · 色标传感器</a> 
					<span>｜</span>
										<a href="/guangxianchuanganqi/">至璨 · 光纤传感器</a> 
					<span>｜</span>
						
				</div>
			</div>
			<div class="cle"></div>
		</div>

	</div>

	<script type="text/javascript">

		function isSearchShow() {

			let SearchBox = document.getElementById('SearchBox'),

				SearchInput = document.getElementById('SearchInput');

			document.addEventListener('click', SearchClick);



			function SearchClick(even) {

				let isSearch = SearchBox.contains(even.target);

				if (isSearch) {

					SearchInput.nextElementSibling.style.display = 'block';

				} else {

					SearchInput.nextElementSibling.style.display = 'none';

				}

			}

		}

		isSearchShow();

	</script>



	<!-- 关于我们 -->
	<div class="idx_about_yt0520">
		<div class="about_bg"><img datatype="img" src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240229/1-2402291A52GI.png" alt="关于我们" class=""></div>
		<div class="about_head w1600 wow fadeInUp" style="visibility: hidden; animation-name: inherit;">
			<div class="idx_title">
				<div class="info">
					<div class="title"><span datatype="span">关于我们</span></div>
					<div class="slogan">致力于为客户提供最前沿的技术创新和优质的产品与服务</div>
					<div class="info_more"><a href="/about/" class="hrefson"><img datatype="hrefs" src="/static/picture/jt_2.png" alt="关于我们"></a></div>
				</div>
				<div class="idx_more hrefson">
					<a href="/about/" class="hrefs">
						<div class="word" datatype="hrefs">
							<span>了解详情</span>
							
						</div>
						<div class="icon"><img datatype="img" src="/static/picture/jt.png" alt="关于我们"></div>
					</a>
				</div>
			</div>
			<div class="about_content">
							<p style="line-height: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;">浙江兆众智能电子科技有限公司（简称“兆众智能”）是一家专注于工业<span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; text-wrap: wrap;">自动化</span>、信息化、<span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; text-wrap: wrap;">智能化</span>及AI智能等领域的高科技创新企业。公司始于2011年，一直致力于为客户提供最前沿的技术创新和优质的产品与服务。</span></p><p style="line-height: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;">兆众智能总部位于杭州未来科技城2.0核心区的甲楠智创中心，地理位置优越，周边汇集众多知名高科技企业和研究机构，如阿里巴巴全球总部、之江实验室、阿里达摩院和南湖脑机交叉研究院等。这种独特的地理环境和创新资源为公司的持续发展提供了强有力的支持。</span></p><p style="line-height: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;">为了完善产业链布局，实现资源整合和协同发展，兆众智能旗下成立多家子公司和研究机构。2011年成立杭州壮一科技，长期专注于国际一流品牌工控自动化产品代理销售及系统集成服务；2020年成立兆众智能产业研究院，以北京理工大学智能机器人研究所石青教授为负责人，联合长三角研究院（嘉兴）博士团队，专注于工业自动化、信息化<span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; text-wrap: wrap;">、</span><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; text-wrap: wrap;">智能化</span>以及智能机器人等领域的创新研究与产业发展。此外，2022年还成立苏州元合成工程科技有限公司，张家港市政府领军人才引进企业，专注科研技术转化和工程应用与推广，拥有多位国内知名行业专家、博士等国际高端人才团队，具有一流的研发能力和产品开发经验。</span></p><p style="line-height: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;"></span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><span style="text-wrap: wrap;">兆众智能一直致力于为全球客户提供最优质可靠的产品和服务。为应对世界百年未有之产业结构大变局，公司不断加强技术研发和产品创新，倾力铸就“至璨 · ZHICAN”品牌旗舰系列，推出高性能、高兼容、超稳定的工业自动化控制类系列产品涵盖：高性能PLC、分布式I/O模块、CPU单元模块、接近传感器、光电传感器、继电器、光纤传感器、色标传感器、激光位移传感器、<span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; text-wrap: wrap;">开关、</span>超声波传感器、视觉传感器、安全产品、工业无线、物联网网关、工业交换机、协议转换器等以及相关的智慧化工业解决方案，广泛应用于智能制造、工业自动化、<span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; text-wrap: wrap;">信息化</span>以及<span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; text-wrap: wrap;">AI智能化</span>等领域。经过多年的创新发展与应用服务，兆众智能的各类自动化与智慧产品已陆续上市，并得到了市场的广泛好评与信任。</span></span></p><p style="line-height: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;">兆众智能始终坚持以客户需求为导向，不断创新和完善产品与服务体系。未来，兆众智能将继续致力于技术创新和优质服务，为客户创造更多价值，推动工业自动化、<span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; text-wrap: wrap;">信息化</span>和<span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; text-wrap: wrap;">智能化</span>的发展。</span></p>			</div>
			<style>
			.about_content p{margin-bottom:10px }
			</style>
		</div>
		<div class="about_main" id="aboutBox">
			<div class="about_img">
				<div class="pin-spacer" style="order: 0; place-self: auto; grid-area: auto; z-index: 1; float: none; flex-shrink: 1; display: block; margin: 0px; inset: 0px; position: absolute; flex-basis: auto; overflow: visible; box-sizing: border-box; width: 1270px; height: 1440px; padding: 0px 0px 720px;"><div class="fixed_wrap bjall" style="transform: translate3d(0px, 0px, 0px); inset: 0px auto auto 0px; margin: 0px; max-width: 1270px; width: 1270px; max-height: 720px; height: 720px; padding: 0px;">
				    					<div class="fixed_bg bj2" datatype="bj" data_img="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240723/1-240H3105I4c0.jpg" style="background: url(&quot;//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240723/1-240H3105I4c0.jpg&quot;) no-repeat; width: 1150px;" title="" alt=""></div>
									</div></div>
			</div>
			<div class="about_info up">
				<div class="items" id="items">
					<div class="item" id="item_1" style="opacity: 1;">
						<div class="number" data-swiper-parallax-y="-150" data-swiper-parallax-duration="800" datatype="span">2000+</div>
						<div class="title" data-swiper-parallax-y="-150" data-swiper-parallax-duration="800" datatype="span">自主研发产品</div>
					</div>
					<div class="item" id="item_2" style="opacity: 0;">
						<div class="number" data-swiper-parallax-y="-150" data-swiper-parallax-duration="800" datatype="span">80+</div>
						<div class="title" data-swiper-parallax-y="-150" data-swiper-parallax-duration="800" datatype="span">资质证书</div>
					</div>
					<div class="item" id="item_3" style="opacity: 0;">
						<div class="number" data-swiper-parallax-y="-150" data-swiper-parallax-duration="800" datatype="span">150000</div>
						<div class="title" data-swiper-parallax-y="-150" data-swiper-parallax-duration="800" datatype="span">月产能</div>
					</div>
					<div class="item" id="item_4" style="opacity: 0;">
						<div class="number" data-swiper-parallax-y="-150" data-swiper-parallax-duration="800"><v datatype="span" class="">2500</v><span datatype="span">m<i datatype="span">2</i></span></div>
						<div class="title" data-swiper-parallax-y="-150" data-swiper-parallax-duration="800" datatype="span">生产基地</div>
					</div>
				</div>
			</div>
			<div class="about_nbsp" id="aboutNbsp"></div>
		</div>
	</div>



	<script type="text/javascript">

		$(document).ready(function() {

			function fnMobisSus() {

				function aboutShow() {

					var body_w = $(window).width(),

						body_h = $(window).height(),

						aboutInfo = $('.about_info'),

						aboutNbsp = $('#aboutNbsp'),

						nbspTop = parseInt(aboutNbsp.offset().top),

						position = $(window).scrollTop(),

						size = aboutInfo.find('.item').length,

						c = 'active';



					let aboutInfoTop = aboutInfo.offset().top;

					aboutInfo.find('.item').first().css({'opacity': 1});



					var isTop = true;

					var i = 0,

						speed = 500;



					function idx(i){ return i};



					function isShow(){

						var top = $(document).scrollTop();

						i = (idx(i) === undefined) ? 0 : idx(i);

						if (isTop) {

							if (top <= Math.floor(aboutInfo.offset().top) && i > 0) {

								$(document).scrollTop(aboutInfo.offset().top);

								aboutInfo.addClass(c).removeClass('up');

							} else {

								aboutInfo.removeClass(c).addClass('up');

							}

						} else {

							if (top >= Math.floor(aboutInfo.offset().top) && i < size - 1) {

								$(document).scrollTop(aboutInfo.offset().top);

								aboutInfo.addClass(c);

							} else {

								aboutInfo.removeClass(c);

							}

						}

						if (top >= Math.floor(aboutInfo.offset().top) + body_h + 300) {

							i = size - 1;

							aboutInfo.find('.item').eq(i).css({'opacity': 1 }).siblings().css({'opacity': ''});

						}

					}

					isShow();





					$(document).scroll(function(){

						isShow();

					});



					function wheelDown(){

						isTop = false;

						if(aboutInfo.hasClass(c)){

							if(!aboutInfo.find('.item').is(':animated')){

								i++;

								if(i > size - 1) {

									i = size - 1;

								}

								idx(i);

								aboutInfo.find('.item').eq(i).animate({'opacity': 1, 'top': ''}, speed).siblings().animate({'opacity': ''}, speed);

							}

						}

					}

					function wheelUp() {

						isTop = true;

						if(aboutInfo.hasClass(c)){

							if(!aboutInfo.find('.item').is(':animated')){

								i--;

								if(i <= 0) {

									i = 0;

								}

								idx(i);

								aboutInfo.find('.item').eq(i).animate({'opacity': 1}, speed).siblings().animate({'opacity': ''}, speed);

							}

						}

					}

					var scrollFunc = function(e) {

						e = e || window.event;

						//判断浏览器IE，谷歌滑轮事件

						if (e.wheelDelta) {

							 //当滑轮向上滚动时

							if (e.wheelDelta > 0) {wheelUp(); }

							//当滑轮向下滚动时

							if (e.wheelDelta < 0) {wheelDown(); }

						}

						 //Firefox滑轮事件

						else if (e.detail) {

							 //当滑轮向下滚动时

							if (e.detail > 0) {wheelDown(); }

							 //当滑轮向上滚动时

							if (e.detail < 0) {wheelUp(); }

						}

					}

					//给页面绑定滑轮滚动事件

					if (document.addEventListener) { //firefox  

						document.addEventListener('DOMMouseScroll', scrollFunc, false);

					}

					//滚动滑轮触发scrollFunc方法  //ie 谷歌

					window.onmousewheel = document.onmousewheel = scrollFunc; 



					var body_w = $(window).width()

					var aboutW = $('.about_head').width() / body_w  * 100;

					$('.fixed_bg').css('width', $('.about_head').width());

					gsap.to(".about_img .fixed_wrap", {

						scrollTrigger: {

							trigger: ".about_img .fixed_wrap",

							start: "top top",

							endTrigger: ".about_nbsp",

							end: "bottom bottom",

							scrub: 1,

							pin: true

						},

					});

					// 图

					gsap.to(".about_img .fixed_bg", {

						scrollTrigger: {

							trigger: ".about_img",

							start: "top 80%",

							end: "+=90%",

							scrub: 1,

							onUpdate: function(self) {

								var progress = self.progress.toFixed(3);

								gsap.to($(".about_img .fixed_bg"), 1, {

									width: aboutW + (progress * (100 - aboutW)) + "%",

								});

							},

						},

					});

				}

				if($(window).width() > 990){

					aboutShow();

				}

			}

			fnMobisSus();

		});

	</script>



	<!-- 合作伙伴 -->

	<div class="idx_hzhb_yt0520">
	
		<div class="idx_title w1600 wow fadeInUp" style="visibility: hidden; animation-name: inherit;">
					<div class="info">

				<div class="title"><span>合作伙伴</span></div>

				<div class="slogan">携手行业精英，共创美好未来</div>

			</div>
        			<div class="idx_more">
						<a href="/guestbook/">

					<div class="word">

						<span>了解详情</span>


					</div>

					<div class="icon"><img src="/static/picture/jt.png" alt="在线留言"></div>

				</a>
        			</div>

		</div>

		<div class="hzhb_main">

			<div class="hzhb_content roll">

				<div class="hzhb_list" style="transform: translateX(-453px);">
					
					<div class="item"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240328/1-24032Q04043538.png" alt="20" title="20"></div>

					
					<div class="item"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240328/1-24032Q040432G.png" alt="19" title="19"></div>

					
					<div class="item"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240328/1-24032Q04043B9.png" alt="18" title="18"></div>

					
					<div class="item"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240328/1-24032Q04044232.png" alt="17" title="17"></div>

					
					<div class="item"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240328/1-24032Q040441Q.png" alt="16" title="16"></div>

					
					<div class="item"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240328/1-24032Q04044C0.png" alt="15" title="15"></div>

					
					<div class="item"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240328/1-24032Q04044306.png" alt="14" title="14"></div>

					
					<div class="item"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240328/1-24032Q04044N4.png" alt="13" title="13"></div>

					
					<div class="item"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240328/1-24032Q040443Q.png" alt="12" title="12"></div>

					
					<div class="item"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240328/1-24032Q040442E.png" alt="11" title="11"></div>

					
					

					
					

					
					

					
					

					
					

					
					

					
					

					
					

					
					

					
					

					

					

				<div class="item"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240328/1-24032Q04043538.png" alt="20" title="20"></div><div class="item"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240328/1-24032Q040432G.png" alt="19" title="19"></div><div class="item"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240328/1-24032Q04043B9.png" alt="18" title="18"></div><div class="item"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240328/1-24032Q04044232.png" alt="17" title="17"></div><div class="item"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240328/1-24032Q040441Q.png" alt="16" title="16"></div><div class="item"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240328/1-24032Q04044C0.png" alt="15" title="15"></div><div class="item"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240328/1-24032Q04044306.png" alt="14" title="14"></div><div class="item"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240328/1-24032Q04044N4.png" alt="13" title="13"></div><div class="item"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240328/1-24032Q040443Q.png" alt="12" title="12"></div><div class="item"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240328/1-24032Q040442E.png" alt="11" title="11"></div><div class="item"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240328/1-24032Q04043538.png" alt="20" title="20"></div><div class="item"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240328/1-24032Q040432G.png" alt="19" title="19"></div><div class="item"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240328/1-24032Q04043B9.png" alt="18" title="18"></div><div class="item"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240328/1-24032Q04044232.png" alt="17" title="17"></div><div class="item"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240328/1-24032Q040441Q.png" alt="16" title="16"></div><div class="item"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240328/1-24032Q04044C0.png" alt="15" title="15"></div><div class="item"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240328/1-24032Q04044306.png" alt="14" title="14"></div><div class="item"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240328/1-24032Q04044N4.png" alt="13" title="13"></div><div class="item"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240328/1-24032Q040443Q.png" alt="12" title="12"></div><div class="item"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240328/1-24032Q040442E.png" alt="11" title="11"></div></div>

			</div>

		<div class="hzhb_content roll2"><div class="hzhb_list" style="transform: translateX(-573px);"><div class="item"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240328/1-24032Q04045638.png" alt="10" title="10"></div><div class="item"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240328/1-24032Q04045N2.png" alt="9" title="9"></div><div class="item"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240328/1-24032Q04045396.png" alt="8" title="8"></div><div class="item"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240328/1-24032Q04045633.png" alt="7" title="7"></div><div class="item"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240328/1-24032Q04046114.png" alt="6" title="6"></div><div class="item"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240328/1-24032Q0404B53.png" alt="5" title="5"></div><div class="item"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240328/1-24032Q0404D52.png" alt="4" title="4"></div><div class="item"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240328/1-24032Q0404AS.png" alt="3" title="3"></div><div class="item"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240328/1-24032Q040461Z.png" alt="2" title="2"></div><div class="item"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240328/1-24032Q0404C63.png" alt="1" title="1"></div><div class="item"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240328/1-24032Q04045638.png" alt="10" title="10"></div><div class="item"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240328/1-24032Q04045N2.png" alt="9" title="9"></div><div class="item"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240328/1-24032Q04045396.png" alt="8" title="8"></div><div class="item"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240328/1-24032Q04045633.png" alt="7" title="7"></div><div class="item"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240328/1-24032Q04046114.png" alt="6" title="6"></div><div class="item"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240328/1-24032Q0404B53.png" alt="5" title="5"></div><div class="item"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240328/1-24032Q0404D52.png" alt="4" title="4"></div><div class="item"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240328/1-24032Q0404AS.png" alt="3" title="3"></div><div class="item"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240328/1-24032Q040461Z.png" alt="2" title="2"></div><div class="item"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240328/1-24032Q0404C63.png" alt="1" title="1"></div><div class="item"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240328/1-24032Q04045638.png" alt="10" title="10"></div><div class="item"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240328/1-24032Q04045N2.png" alt="9" title="9"></div><div class="item"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240328/1-24032Q04045396.png" alt="8" title="8"></div><div class="item"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240328/1-24032Q04045633.png" alt="7" title="7"></div><div class="item"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240328/1-24032Q04046114.png" alt="6" title="6"></div><div class="item"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240328/1-24032Q0404B53.png" alt="5" title="5"></div><div class="item"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240328/1-24032Q0404D52.png" alt="4" title="4"></div><div class="item"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240328/1-24032Q0404AS.png" alt="3" title="3"></div><div class="item"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240328/1-24032Q040461Z.png" alt="2" title="2"></div><div class="item"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240328/1-24032Q0404C63.png" alt="1" title="1"></div></div></div></div>

	</div>

	<script type="text/javascript">

		$(document).ready(() => {

			function rollBox() {

				var box = $('.hzhb_main'),

					roll = box.children('.roll'),

					list = roll.children('.hzhb_list'),

					item = list.children(),

					w = item.outerWidth();



				var addLabel = `<div class="hzhb_content roll2"><div class="hzhb_list"></div></div>`;

				box.append(addLabel);



				var roll_2 = box.children('.roll2');

				{

					let size = (item.length % 2 != 0) ? item.length - 1 : item.length;

					for (let idx = size / 2; idx < size; idx++) {

						roll_2.children().append(item.eq(idx).clone());

						item.eq(idx).remove();

					}

				}



				{

					item = list.children();

					list.append(item.clone(), item.clone());



					let list_2 = roll_2.children(),

						item_2 = list_2.children();

					list_2.css({'transform': `translateX(${-w/2}px)`});

					list_2.append(item_2.clone(), item_2.clone());



					let size_one = item.length,

						size_two = item_2.length;



					let list_w = w * size_one,

						list_2_w = w * size_two;

					function roll() {

						var i_one = 0,

							i_two = w / 2;

						setInterval(() => {

							i_one++;

							if (i_one > list_w) {i_one = 1 };

							i_two++;

							if (i_two > list_2_w) {i_two = 1 };

							list.css({'transform': `translateX(${-i_one}px)`});

							list_2.css({'transform': `translateX(${-i_two}px)`});

						}, 25);

					}

					roll();

				}

			}

			rollBox();

		});

	</script>




	<div class="idx_news_yt0520">

		<div class="news_main w1600">


			<div class="news_head">

				<div class="idx_title wow fadeInUp" style="visibility: hidden; animation-name: inherit;">

					<div class="info">

						<div class="title"><span>公司动态</span></div>

						<div class="slogan">

							 永远向用户提供满足其需求的产品和良好的售后服务。 						</div>

						<div class="info_more"><a href="/gongsidongtai/"><img src="/static/picture/jt_2.png" alt="公司动态"></a></div>

					</div>

				</div>

				<div class="news_list">

										<div class="item wow fadeInUp" style="visibility: hidden; animation-name: inherit;">

						<div class="img"><div class="center"><div class="ab"><a href="/about/482.html"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20250211/1-2502111P334X2.png" title="【至璨 · ZHICAN】工业控制产品助力新能源汽车智能制造升级!" alt="【至璨 · ZHICAN】工业控制产品助力新能源汽车智能制造升级!"></a></div></div></div>

						<div class="info">

							<div class="time">2025-02-11</div>

							<div class="title"><a href="/about/482.html">【至璨 · ZHICAN】工业控制产品助力新能源汽车智能制造升级</a></div>

						</div>

					</div>

										
				</div>

			</div>




			<div class="move"></div>
			<div class="news_content">

				<div class="new_title wow fadeInUp" style="visibility: hidden; animation-name: inherit;"><a href="/xinpinfabu/">新品发布</a></div>

				<div class="news_swiper">

					<div class="swiper_img wow fadeInUp" style="visibility: hidden; animation-name: inherit;">

						<div class="content">

							<div class="center">

								<div class="swiper_img_list swiper-container-fade swiper-container-initialized swiper-container-horizontal" id="newsSwiperImg">

									<div class="swiper-wrapper">

																				
									</div>

								<span class="swiper-notification" aria-live="assertive" aria-atomic="true"></span></div>

							</div>

						</div>

					</div>

					<div class="swiper_title wow fadeInUp" style="visibility: hidden; animation-name: inherit;">

						<div class="news_list swiper-container-initialized swiper-container-vertical swiper-container-free-mode swiper-container-thumbs" id="newsSwiperText">

							<div class="swiper-wrapper">
														</div>

						<span class="swiper-notification" aria-live="assertive" aria-atomic="true"></span></div>

					</div>

				</div>

			</div>
		</div>

	</div>

	<script type="text/javascript">

		function newsSwiper() {
			var newsSwiperText = $('#newsSwiperText'),
				c = 'active';
			var thumbsSwiper = new Swiper(newsSwiperText, {
				direction: 'vertical',
				slidesPerView: 4,
				freeMode: true,
				watchSlidesVisibility: true,
				watchSlidesProgress: true,
				breakpoints: {
					768: {
						slidesPerView: 4
					},
					990: {
						slidesPerView: 3
					}

				}

			})

			var newsSwiper = new Swiper('#newsSwiperImg', {
				speed: 500,
				autoplay: {
					delay: 4500,
					disableOnInteraction: false,
				},

				effect: 'fade',
				fadeEffect: {
					crossFade: true,
				},
				thumbs: {
					swiper: thumbsSwiper
				},

				on: {
					slideChangeTransitionStart() {
						labelAddClass(this.activeIndex);
					}
				}
			});



			function labelAddClass(i){
				newsSwiperText.find('.swiper-slide').eq(i).addClass(c).siblings().removeClass(c);
			}
		}
		newsSwiper();
	</script>



	<div class="bjall">
		<div class="idx_advert_yt0520 bj2" datatype="bj" data_img="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240108/1-24010QS635U0.jpg" style="background: url(//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240108/1-24010QS635U0.jpg) no-repeat center / cover;">
			<div class="advert_bg" datatype="span">ZHAOZHONG</div>
			<div class="advert_mian w1600">
				<div class="advert_info wow fadeInUp" style="visibility: hidden; animation-name: inherit;">
					<div class="title" datatype="span">快速交付与全面支持</div>
					<div class="txt" datatype="span">兆众智能通过现场操作指导和售后技术支持，为客户提供从产品选择到生产线运行的全程支持。</div>
				</div>
				<div class="advert_items wow fadeInUp" style="visibility: hidden; animation-name: inherit;">
					<div class="advert_one">
					<div class="title" datatype="span">服务支持</div>						<div class="more active hrefall">
							<a href="/ziliaoxiazai1/" class="hrefson">
								<div class="txt" datatype="hrefs">资料下载中心</div>
								<div class="icon"><i></i><i></i></div>
							</a>
						</div>
						
					</div>
										<div class="advert_one">
						<div class="title" datatype="span">免费试用</div>
						<div class="more active hrefall">
							<a href="/mianfeishiyong/" class="hrefson">
								<div class="txt" datatype="hrefs">申请免费试用</div>
								<div class="icon"><i></i><i></i></div>
							</a>
						</div>
					</div>
									</div>
			</div>
		</div>
	</div>
<script>

$(".head_wap > .h_bot > .h_nav > li:eq(0)").addClass('on').siblings().removeClass('on');
</script>
<div id="footer">
    <!-- 底部 -->
<div class="footer_yt0520">
	<div class="footer_main w1600">
		<div class="footer_head">
					<div class="footer_title wow fadeInUp" style="visibility: hidden; animation-name: inherit;">
				<div class="title"><span>CONTACT US</span></div>
				<div class="slogan">
					<p>我们以可靠品质成就客户<em>，</em><br>以优秀服务赢得市场<em>。</em></p></div>
			</div>
			
			<div class="footer_nav wow fadeInUp" style="visibility: hidden; animation-name: inherit;">
				<ul>
									<li>
						<div class="title"><a href="/solution/">应用案例</a></div>
						<div class="menu">
													<a href="/cases1/">行业应用案例</a>
													<a href="/cases2/">检测实验应用教学</a>
													<a href="/cases3/">产品拓扑图</a>
												</div>
					</li>
									<li>
						<div class="title"><a href="/service/">服务支持</a></div>
						<div class="menu">
													<a href="/download/">资料下载</a>
													<a href="/fuwujieshao/">服务介绍</a>
													<a href="/dinggouzixun/">订购咨询</a>
													<a href="/mianfeishiyong/">免费试用</a>
													<a href="/changjianwenti/">常见问题</a>
													<a href="/zaixianliuyan/">在线留言</a>
												</div>
					</li>
									<li>
						<div class="title"><a href="/make/">研发制造</a></div>
						<div class="menu">
													<a href="/yanfashili/">研发实力</a>
													<a href="/kehudingzhi/">客户定制</a>
													<a href="/jingmizhizao/">精密制造</a>
													<a href="/pinzhibaozhang/">品质保障</a>
												</div>
					</li>
									<li>
						<div class="title"><a href="/about/">关于我们</a></div>
						<div class="menu">
													<a href="/abouts/">公司简介</a>
													<a href="/qiyewenhua/">企业文化</a>
													<a href="/fazhanlicheng/">发展历程</a>
													<a href="/zuzhijiagou/">组织架构</a>
													<a href="/join/">招贤纳士</a>
													<a href="/News/"> 新闻中心</a>
												</div>
					</li>
								</ul>
			</div>
			<div class="qrcode">
				<!--<div class="one">-->
				<!--	<div class="img"><img src="static/picture/64fbd41c2bb08.png" alt="BOJKE博亿精科"></div>-->
				<!--	<div class="title">微信在线客服</div>-->
				<!--</div>-->
				<div class="one">
					<div class="img"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240226/1-240226111319411.jpg" alt="微信公众号"></div>
					<div class="title">微信公众号</div>
				</div>
			</div>
		</div>
		<div class="footer_body wow fadeInUp" style="visibility: hidden; animation-name: inherit;">
			<div class="info">
				<div class="contact">
					<div class="one">
						<div class="icon"><img src="/static/picture/icon_mail.png" alt="联系邮箱"></div>
						<div class="txt"><EMAIL></div>
					</div>
					<div class="one">
						<div class="icon"><img src="/static/picture/icon_phone.png" alt="售前咨询"></div>
						<div class="txt">0571-88640980</div>
					</div>
					<div class="one" style="width: 100%;">
						<div class="icon"><img src="/static/picture/icon_address.png" alt="联系地址"></div>
						<div class="txt">浙江省杭州市余杭区中泰工业区甲楠智创中心A座5层</div>
					</div>
				</div>
				<div class="copy pc">Copyright © 2024 浙江兆众智能电子科技有限公司 
All Rights Reserved　版权所有　<a href="https://beian.miit.gov.cn/" rel="nofollow" target="_blank">浙ICP备17057674号-2</a>　　			  　　<a href="/yinsitiaokuan/">隐私条款</a>　　
			     			  　　<a href="/falvshengming/">法律声明</a>　　
			      
			  </div>
				<div class="copy mobile">
				    <p>Copyright © 2024 浙江兆众智能电子科技有限公司 
All Rights Reserved　版权所有</p>
				    <p><a href="https://beian.miit.gov.cn/" rel="nofollow" target="_blank">浙ICP备17057674号-2</a>　　</p>
				    <p>
										　　<a href="javascript:;">隐私条款</a>　　
					 					　　<a href="javascript:;">法律声明</a>　　
					  
					</p>
				</div>
			</div>
			<div class="qrcode">
				<!--<div class="one">-->
				<!--	<div class="img"><img src="static/picture/64fbd41c2bb08.png" alt="BOJKE博亿精科"></div>-->
				<!--	<div class="title">微信在线客服</div>-->
				<!--</div>-->
				<div class="one">
					<div class="img"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240226/1-240226111319411.jpg" alt="微信公众号"></div>
					<div class="title">微信公众号</div>
				</div>
			</div>
		</div>
	</div>
</div>
<script type="text/javascript">
	const wow = new WOW({
		boxClass: 'wow', //需要执行动画的元素的 class
		animateClass: 'animated', //animation.css 动画的 class
		offset: 0, //距离可视区域多少开始执行动画
		mobile: false, //是否在移动设备上执行动画
		live: true //异步加载的内容是否有效
	})
	wow.init()
</script>

<!-- 客服挂件 -->
<div class="pendant_box">
	<div class="zixun_link">
			<a href="/mianfeishiyong/">
			<div class="icon"><i class="iconfont icon_kefu"></i></div>
			<div class="title">免费试用</div>
		</a>
		
	</div>
	<div class="zixun_hover">
		<!-- 微信咨询 -->
		<div class="item wx_zixun">
			<div class="head">
				<div class="icon"><i class="iconfont icon_erweima"></i></div>
				<div class="title">微信咨询</div>
			</div>
			<div class="info">
				<div class="img"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20250611/1-2506111046123Y.png" alt="微信咨询"></div>
				<div class="txt">扫一扫添加微信</div>
			</div>
		</div>
		<div class="line"></div>
		<!-- 电话咨询 -->
		<div class="item phone_zixun">
			<div class="head">
				<div class="icon"><i class="iconfont icon_kefu"></i></div>
				<div class="title">电话咨询</div>
			</div>
			<div class="info">
				<div class="info_li">
					<div class="title">
						<div class="icon"><img src="static/picture/icon_dh.png" alt="售前咨询"></div>
						<div class="txt">
							<div class="b1">售前咨询</div>
							<div class="b2">0571-88640980</div>
						</div>
					</div>
				</div>
				<div class="info_li">
					<div class="title">
						<div class="icon"><img src="/static/picture/icon_lx.png" alt="我们联系您"></div>
						<div class="txt">
							<div class="b1">我们联系您</div>
						</div>
					</div>
					<div class="form">
    <form method="POST" enctype="multipart/form-data" action="/index.php?m=home&amp;c=Lists&amp;a=gbook_submit&amp;lang=cn" onsubmit="return submit3e46aca21dbe44be2acdfb128082328b(this);" class="layui-form">
							<input type="text" class="layui-input float2" id="con_phone" name="输入手机号" placeholder="输入手机号" style="">
							<button class="layui-btn" type="submit">立即提交</button>
        <input type="hidden" name="gourl" id="gourl_3e46aca21dbe44be2acdfb128082328b" value="https%3A%2F%2Fwww.zhaozhongai.com%2F" style=""><input type="hidden" name="typeid" value="47" style=""><input type="hidden" name="__token__3e46aca21dbe44be2acdfb128082328b" id="3e46aca21dbe44be2acdfb128082328b" value="490d1ccbe8bf94658e8d187668384f86" style=""><input type="hidden" name="form_type" value="0" style=""><script type="text/javascript">
    function submit3e46aca21dbe44be2acdfb128082328b(elements)
    {
        if (document.getElementById('gourl_3e46aca21dbe44be2acdfb128082328b')) {
            document.getElementById('gourl_3e46aca21dbe44be2acdfb128082328b').value = encodeURIComponent(window.location.href);
        }
            var x = elements;
    for (var i=0;i<x.length;i++) {
        
                            if(x[i].name == 'attr_34' && x[i].value.length == 0){
                                alert('输入手机号不能为空！');
                                return false;
                            }
                         
                    if(x[i].name == 'attr_34' && !(/^([\d\-\+]+)$/.test( x[i].value)) && x[i].value.length > 0){
                        alert('输入手机号格式不正确！！');
                        return false;
                    }
                   
    }
        
        elements.submit();
    }

    function ey_fleshVerify_1754026153(id)
    {
        var token = id.replace(/verify_/g, '__token__');
        var src = "/index.php?m=api&c=Ajax&a=vertify&type=guestbook&lang=cn&token="+token;
        src += "&r="+ Math.floor(Math.random()*100);
        document.getElementById(id).src = src;
    }

    function f7874bf57607a4d75ef99cc2a22872489()
    {
        var ajax = new XMLHttpRequest();
        ajax.open("post", "/index.php?m=api&c=Ajax&a=get_token", true);
        ajax.setRequestHeader("X-Requested-With","XMLHttpRequest");
        ajax.setRequestHeader("Content-type","application/x-www-form-urlencoded");
        ajax.send("name=__token__3e46aca21dbe44be2acdfb128082328b");
        ajax.onreadystatechange = function () {
            if (ajax.readyState==4 && ajax.status==200) {
                document.getElementById("3e46aca21dbe44be2acdfb128082328b").value = ajax.responseText;
                document.getElementById("gourl_3e46aca21dbe44be2acdfb128082328b").value = encodeURIComponent(window.location.href);
          　}
        } 
    }
    f7874bf57607a4d75ef99cc2a22872489();
    function getNext1598839807(id,name,level) {
        var input = document.getElementById('attr_'+name);
        var first = document.getElementById('first_id_'+name);
        var second = document.getElementById('second_id_'+name);
        var third = document.getElementById('third_id_'+name);
        var findex ='', fvalue = '',sindex = '',svalue = '',tindex = '',tvalue = '',value='';

        if (level == 1){
            if (second) {
                second.style.display = 'none';
                second.innerHTML  = ''; 
            }
            if (third) {
                third.style.display = 'none';
                third.innerHTML  = '';
            }
            findex = first.selectedIndex;
            fvalue = first.options[findex].value;
            input.value = fvalue;
            value = fvalue;
        } else if (level == 2){
            if (third) {
                third.style.display = 'none';
                third.innerHTML  = '';
            }
            findex = first.selectedIndex;
            fvalue = first.options[findex].value;
            sindex = second.selectedIndex;
            svalue = second.options[sindex].value;
            if (svalue) {
                input.value = fvalue+','+svalue;
                value = svalue;
            }else{
                input.value = fvalue;
            }
        } else if (level == 3){
            findex = first.selectedIndex;
            fvalue = first.options[findex].value;
            sindex = second.selectedIndex;
            svalue = second.options[sindex].value;
            tindex = third.selectedIndex;
            tvalue = third.options[tindex].value;
            if (tvalue) {
                input.value = fvalue+','+svalue+','+tvalue;
                value = tvalue;
            }else{
                input.value = fvalue+','+svalue;
            }
        } 
        if (value) {
            if(document.getElementById(id))
            {
                document.getElementById(id).options.add(new Option('请选择','')); 
                var ajax = new XMLHttpRequest();
                ajax.open("post", "/index.php?m=api&c=Ajax&a=get_region", true);
                ajax.setRequestHeader("X-Requested-With","XMLHttpRequest");
                ajax.setRequestHeader("Content-type","application/x-www-form-urlencoded");
                ajax.send("pid="+value);
                ajax.onreadystatechange = function () {
                    if (ajax.readyState==4 && ajax.status==200) {
                        var data = JSON.parse(ajax.responseText).data;
                        if (data) {
                            data.forEach(function(item) {
                                document.getElementById(id).options.add(new Option(item.name,item.id)); 
                                document.getElementById(id).style.display = "block";
                            });
                        }
                  　}
                }
            }
        }
    }
</script>    </form>
					</div>
				</div>
			</div>
		</div>
	</div>
	<div class="scroll_top" id="scrollTopBtn"><i class="iconfont icon_icon_top"></i></div>
</div>
<script type="text/javascript">
	$(document).ready(function() {
		function clickScrollTop(){
			var btn = $('#scrollTopBtn');
			function isShow(){
				if($(document).scrollTop() > 100){
					btn.stop().fadeIn(300);
				} else {
					btn.stop().fadeOut(300);
				}
			}
			isShow();
			$(document).scroll(function(){ isShow() });
			btn.click(function(){
				$('html, body').animate({scrollTop: 0}, 500);
			});
		}
		clickScrollTop();
	});
</script>
</div>

<script type="text/javascript" src="/static/js/form.js"></script>
<script type="text/javascript" src="/static/js/footer.js"></script>
<script language="javascript" type="text/javascript" src="/template/default/pc/static/js/pages.js"></script>
<script>
    $(".nav li:last a").removeClass("active");
</script>
<script>
var _hmt = _hmt || [];
(function() {
  var hm = document.createElement("script");
  hm.src = "https://hm.baidu.com/hm.js?71717ad93e343f6e3d55e6a8e630767f";
  var s = document.getElementsByTagName("script")[0]; 
  s.parentNode.insertBefore(hm, s);
})();
</script>

<script type="text/javascript">var root_dir="";var ey_aid=0;</script>
<script language="javascript" type="text/javascript" src="/public/static/common/js/ey_footer.js?v=v1.7.4"></script>

 

</body></html>