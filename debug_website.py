#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
<AUTHOR> 
@license          http://www.178188.xyz
@lastmodify       2025年8月1日
模块说明: 调试网站结构
"""

import asyncio
import logging
from playwright.async_api import async_playwright

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def debug_website():
    """调试网站结构"""
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False)  # 显示浏览器
        page = await browser.new_page()
        
        try:
            logger.info("访问网站...")
            await page.goto("https://www.zhaozhongai.com/down/", wait_until='networkidle')
            
            # 等待页面加载
            await asyncio.sleep(5)
            
            logger.info("获取页面HTML结构...")
            
            # 获取页面标题
            title = await page.title()
            logger.info(f"页面标题: {title}")
            
            # 查找所有链接
            links = await page.query_selector_all('a')
            logger.info(f"找到 {len(links)} 个链接")
            
            pdf_links = []
            for link in links:
                href = await link.get_attribute('href')
                text = await link.inner_text()
                if href and '.pdf' in href:
                    pdf_links.append({
                        'text': text.strip(),
                        'href': href
                    })
            
            logger.info(f"找到 {len(pdf_links)} 个PDF链接:")
            for i, link in enumerate(pdf_links[:10]):  # 只显示前10个
                logger.info(f"  {i+1}. {link['text']} -> {link['href']}")
            
            # 查找所有图片
            images = await page.query_selector_all('img')
            logger.info(f"找到 {len(images)} 个图片")
            
            # 查找包含"至璨"的元素
            zhican_elements = await page.query_selector_all('*')
            zhican_count = 0
            for element in zhican_elements:
                try:
                    text = await element.inner_text()
                    if text and "至璨" in text and len(text.strip()) < 100:
                        zhican_count += 1
                        if zhican_count <= 5:  # 只显示前5个
                            logger.info(f"至璨元素: {text.strip()}")
                except:
                    continue
            
            logger.info(f"总共找到 {zhican_count} 个包含'至璨'的元素")
            
            # 尝试点击第一个分类
            logger.info("尝试点击第一个分类...")
            cpu_element = await page.query_selector('li:has-text("CPU单元")')
            if cpu_element:
                await cpu_element.click()
                logger.info("成功点击CPU单元分类")
                
                # 等待页面变化
                await asyncio.sleep(3)
                
                # 再次查找PDF链接
                new_links = await page.query_selector_all('a[href*=".pdf"]')
                logger.info(f"点击后找到 {len(new_links)} 个PDF链接")
                
                for i, link in enumerate(new_links[:5]):
                    href = await link.get_attribute('href')
                    text = await link.inner_text()
                    logger.info(f"  新链接 {i+1}. {text.strip()} -> {href}")
            
            # 保存页面截图
            await page.screenshot(path="website_debug.png")
            logger.info("页面截图已保存: website_debug.png")
            
            # 保存页面HTML
            html_content = await page.content()
            with open("website_debug.html", "w", encoding="utf-8") as f:
                f.write(html_content)
            logger.info("页面HTML已保存: website_debug.html")
            
        except Exception as e:
            logger.error(f"调试过程出错: {e}")
            
        finally:
            await browser.close()

if __name__ == "__main__":
    asyncio.run(debug_website())
