<!DOCTYPE html><html lang="en"><head>
    <meta charset="UTF-8">
    <meta http-equiv="Cache-Control" content="no-transform">
    <meta http-equiv="Cache-Control" content="no-siteapp">
    <meta name="applicable-device" content="mobile">
    <meta name="renderer" content="webkit">
    <meta content="email=no" name="format-detection">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta http-equiv="Content-Language" content="zh-CN">
    <meta content=" " name="design">
    <meta name="viewport" content="width=device-width,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="format-detection" content="telephone = no">
	<!--公共js-->
    <!-- 字体图标 -->
<link rel="shortcut icon" href="/favicon.ico">
<link rel="stylesheet" href="https://at.alicdn.com/t/font_3312555_r19ypx0xkc.css">
<link href="/template/default/pc/static/css/swiper.min.css" rel="stylesheet" media="screen" type="text/css">
<link href="/template/default/pc/static/css/animate.min.css" rel="stylesheet" media="screen" type="text/css">
<link href="/template/default/pc/static/css/iconfont.css" rel="stylesheet" media="screen" type="text/css">
<link href="/template/default/pc/static/css/layui.css" rel="stylesheet" media="screen" type="text/css">
<link href="/template/default/pc/static/css/style.css" rel="stylesheet" media="screen" type="text/css">
<script src="https://hm.baidu.com/hm.js?71717ad93e343f6e3d55e6a8e630767f"></script><script src="https://zz.bdstatic.com/linksubmit/push.js"></script><script language="javascript" type="text/javascript" src="/template/default/pc/static/js/jquery-3.3.1.min.js"></script>
<script language="javascript" type="text/javascript" src="/template/default/pc/static/js/swiper4.5.3.min.js"></script>
<script language="javascript" type="text/javascript" src="/template/default/pc/static/js/wow.js"></script>
<script language="javascript" type="text/javascript" src="/template/default/pc/static/js/common.js"></script>
<script language="javascript" type="text/javascript" src="/template/default/pc/static/js/layui.js"></script><link id="layuicss-laydate" rel="stylesheet" href="https://www.zhaozhongai.com/template/default/pc/static/js/css/modules/laydate/default/laydate.css?v=5.3.1" media="all"><link id="layuicss-layer" rel="stylesheet" href="https://www.zhaozhongai.com/template/default/pc/static/js/css/modules/layer/default/layer.css?v=3.5.1" media="all"><link id="layuicss-skincodecss" rel="stylesheet" href="https://www.zhaozhongai.com/template/default/pc/static/js/css/modules/code.css?v=2" media="all">
<script language="javascript" type="text/javascript" src="/template/default/pc/static/js/common1.js"></script>
<script language="javascript" type="text/javascript" src="/template/default/pc/static/js/vue.min.js"></script>
<script language="javascript" type="text/javascript" src="/template/default/pc/static/js/gsap.min.js"></script>
<script language="javascript" type="text/javascript" src="/template/default/pc/static/js/gsap.registerplugin.js"></script>
<script language="javascript" type="text/javascript" src="/template/default/pc/static/js/tweenmax.min.js"></script>
<script language="javascript" type="text/javascript" src="/template/default/pc/static/js/jquery.superscrollorama.js"></script>
<script language="javascript" type="text/javascript" src="/template/default/pc/static/js/common2.js"></script>


<!-- 分享 -->
    <style>
        .layui-laypage .layui-laypage-prev i, .layui-laypage .layui-laypage-next i{
            vertical-align: top;
            font-size: 14px;
        }

       
        .layui-laypage .layui-laypage-prev, .layui-laypage .layui-laypage-next{
            width: 30px;
            padding: 0;
            border: 1px solid #494b4c;
            border-radius: 0;
            border-radius: 0 !important;
        }

        .layui-laypage .layui-laypage-prev:hover, .layui-laypage .layui-laypage-next:hover {
            background: #2b37d8;
            border-color: #2b37d8;
            color: #fff !important;
        }

        .layui-laypage .layui-laypage-prev:hover i, .layui-laypage .layui-laypage-next:hover i {
            border-color: #fff !important;
        }

        .layui-laypage a, .layui-laypage span {
            color: #333  !important;
        }


    </style>

	<script>
		window.conf = {"ROOT":"","STYLE":"/wstmart/home/<USER>/default","APP":"","STATIC":"/static","SUFFIX":"html","SMS_VERFY":"","SMS_OPEN":"","GOODS_LOGO":"","SHOP_LOGO":"","MALL_LOGO":"upload/sysconfigs/2022-06/62a3f784da502.png","USER_LOGO":"","IS_LOGIN":"0","TIME_TASK":"1","ROUTES":'{"admin\/index\/login":"login_3","home\/index\/index":"\/","home\/product\/index":"product\/[:id]","home\/product\/index2":"productlist\/[:id]","home\/product\/detail":"productinfo<id>","home\/solution\/index":"solution\/[:id]","home\/solution\/detail":"casesinfo<id>","home\/news\/index":"news\/[:id]","home\/news\/detail":"newsinfo<id>","home\/about\/faq":"faq","home\/download\/index":"download\/[:id]","home\/about\/index":"about","home\/about\/service":"service","home\/about\/make":"make","home\/about\/feedback":"feedback","home\/contact\/index":"contact","home\/about\/join":"join","home\/about\/order":"order","home\/about\/trial":"trial","home\/about\/stop":"stop","home\/about\/ability":"ability","home\/honor\/index":"honor\/[:id]","home\/solution\/cases":"cases\/[:id]","home\/index\/Privacy_Policy":"privacyPolicy","home\/index\/Legal_Notices":"legalNotices","home\/search\/index":"search\/[:keys]","home\/search\/tags":"tag<id>","home\/search\/tagsAll":"tagAll","home\/sitmap\/index":"sitemap"}'}
	</script>
<title>至璨 · CPU单元_“至璨/ZHICAN”工业品牌旗舰系列-“至璨/ZHICAN”工业品牌旗舰系列</title>
<meta name="description" content="">
<meta name="keywords" content="">
<script type="text/javascript" src="/public/static/common/js/ey_global.js?v=v1.7.4"></script>
</head>

<body>
 <div id="header">
    <!-- 头部 PC -->

<div class="header_yt0516 active">
	<!-- 头部内容 -->
	<div class="header_main w1800 ">
		<div class="logo">
		<a href="https://www.zhaozhongai.com"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240226/1-240226113354108.png" alt="“至璨/ZHICAN”工业品牌旗舰系列"><img class="active" src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240229/1-2402291641263c.png" alt="“至璨/ZHICAN”工业品牌旗舰系列"></a>
		</div>
		<div class="logo_text">专注于工业智能化、信息化、自动化及AI智能等领域</div>
		<div class="column qtwy-header">
			<!-- PC导航 -->
			<div class="nav">
				<ul>
<li><a href="https://www.zhaozhongai.com">首页</a></li>


<li><a href="/solution/" class="">应用案例</a>

<div class="nav_menu">
	<div class="center">
		<div class="content w1600">
			<div class="c_left">
				<div class="info">
					<div class="title">应用案例</div>
					<div class="txt">根据用户需求选择适合您的产品；帮助用户创造价值，用户因为我们而实现更大的梦想。
</div>
				</div>
				<div class="list">
									<div class="link"><a href="/cases1/">行业应用案例<div class="icon"></div></a></div>
									<div class="link"><a href="/cases2/">检测实验应用教学<div class="icon"></div></a></div>
									<div class="link"><a href="/cases3/">产品拓扑图<div class="icon"></div></a></div>
								</div>
			</div>
			<div class="img">
				<div class="pb">
					<div class="ab">
					<a href="/solution/"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240226/1-240226154R2L9.png" alt="/solution/"></a>
					</div>
				</div>
			</div>
				<div class="menu_list">
					<div class="menu_title">其他咨询服务？</div>
					<div class="items">
												<div class="item">
							<a href="/mianfeishiyong/">
								<div class="icon"><img src="/static/picture/icon_ser_4.png" alt="免费试用"></div>
								<div class="title">免费试用</div>
							</a>
						</div>
												<div class="item">
							<a href="/product/">
								<div class="icon"><img src="/static/picture/icon_ser_2.png" alt="产品中心"></div>
								<div class="title">产品中心</div>
							</a>
						</div>
												<div class="item">
							<a href="/dinggouzixun/">
								<div class="icon"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240103/1-2401031G42TQ.png" alt="订购咨询"></div>
								<div class="title">订购咨询</div>
							</a>
						</div>
											</div>
				</div>
</div>
<div class="r_bg"></div>
</div>
</div>


</li>




<li><a href="/product/" class="active">产品中心</a>

<div class="nav_menu">
	<div class="center">
		<div class="content w1600">
			<div class="c_left">
				<div class="info">
					<div class="title">产品中心</div>
					<div class="txt">兆众智能一直遵循产品质量是设计进去，制造出来的品质理念；不断钻研专业技术，加快工业自动化的国产化进程。</div>
				</div>
				<div class="list">
									<div class="link"><a href="/productlist1/">至璨 · CPU单元<div class="icon"></div></a></div>
									<div class="link"><a href="/productlist2/">至璨 · 总线耦合器<div class="icon"></div></a></div>
									<div class="link"><a href="/mokuai/">至璨 · IO模块<div class="icon"></div></a></div>
									<div class="link"><a href="/jiejinchuanganqi/">至璨 · 接近传感器<div class="icon"></div></a></div>
									<div class="link"><a href="/guangdianchuanganqi/">至璨 · 光电传感器<div class="icon"></div></a></div>
									<div class="link"><a href="/-chong--lu-juan----hong--hu-dian-/">至璨 · 继电器<div class="icon"></div></a></div>
									<div class="link"><a href="/jiguangguangdianchuanganqi/">至璨 · 激光光电传感器<div class="icon"></div></a></div>
									<div class="link"><a href="/jiguangweiyichuanganqi/">至璨 · 激光位移传感器<div class="icon"></div></a></div>
									<div class="link"><a href="/sebiaochuanganqi/">至璨 · 色标传感器<div class="icon"></div></a></div>
									<div class="link"><a href="/guangxianchuanganqi/">至璨 · 光纤传感器<div class="icon"></div></a></div>
									<div class="link"><a href="/-chong--lu-huan--liao---/">至璨 · 微动/行程/限位开关<div class="icon"></div></a></div>
									<div class="link"><a href="/-chong--lu-bing--liao-donghuan-----/">至璨 · 管道液位传感器<div class="icon"></div></a></div>
								</div>
			</div>
			<div class="img">
				<div class="pb">
					<div class="ab">
					<a href="/product/"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240226/1-240226154R2L9.png" alt="/product/"></a>
					</div>
				</div>
			</div>
				<div class="menu_list">
					<div class="menu_title">其他咨询服务？</div>
					<div class="items">
												<div class="item">
							<a href="/mianfeishiyong/">
								<div class="icon"><img src="/static/picture/icon_ser_4.png" alt="免费试用"></div>
								<div class="title">免费试用</div>
							</a>
						</div>
												<div class="item">
							<a href="/product/">
								<div class="icon"><img src="/static/picture/icon_ser_2.png" alt="产品中心"></div>
								<div class="title">产品中心</div>
							</a>
						</div>
												<div class="item">
							<a href="/dinggouzixun/">
								<div class="icon"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240103/1-2401031G42TQ.png" alt="订购咨询"></div>
								<div class="title">订购咨询</div>
							</a>
						</div>
											</div>
				</div>
</div>
<div class="r_bg"></div>
</div>
</div>


</li>




<li><a href="/service/" class="">服务支持</a>


<ul>
<li><a href="/download/">资料下载</a></li>
<li><a href="/fuwujieshao/">服务介绍</a></li>
<li><a href="/dinggouzixun/">订购咨询</a></li>
<li><a href="/mianfeishiyong/">免费试用</a></li>
<li><a href="/changjianwenti/">常见问题</a></li>
<li><a href="/zaixianliuyan/">在线留言</a></li>
</ul>

</li>




<li><a href="/make/" class="">研发制造</a>


<ul>
<li><a href="/yanfashili/">研发实力</a></li>
<li><a href="/kehudingzhi/">客户定制</a></li>
<li><a href="/jingmizhizao/">精密制造</a></li>
<li><a href="/pinzhibaozhang/">品质保障</a></li>
</ul>

</li>




<li><a href="/about/" class="">关于我们</a>


<ul>
<li><a href="/abouts/">公司简介</a></li>
<li><a href="/qiyewenhua/">企业文化</a></li>
<li><a href="/fazhanlicheng/">发展历程</a></li>
<li><a href="/zuzhijiagou/">组织架构</a></li>
<li><a href="/join/">招贤纳士</a></li>
<li><a href="/News/"> 新闻中心</a></li>
</ul>

</li>




<li><a href="/contact1/" class="">联系我们</a>


<ul>
<li><a href="/contact/">联系方式</a></li>
<li><a href="/guestbook/">在线留言</a></li>
</ul>

</li>




	
</ul>
</div>
			<!-- 电话 -->
			<div class="phone">
				<div class="center">
				    <div class="icon"><i class="iconfont icon_dianhua"></i></div>
					<div class="number"><span>0571-88640980</span></div>
				</div>
			</div>
			<!-- 语言 -->
			<div class="lang">
				<div class="center">
					<div class="icon"></div>
									</div>
			</div>
			<!-- 菜单 -->
			<div class="menu">
				<div class="icona" id="menuBtn"><i class="iconfont icon_sousuo"></i></div>
				<div class="menu_box" id="menuList">
					<div class="center w1600">
						<div class="menu_info">
							<div class="info_title"><span>感知世界，连接未来</span></div>
							<div class="items">
								<ul>
									<li>
										<div class="head">
											<div class="icon"><img src="/static/picture/icon_contact_address.png" alt="“至璨/ZHICAN”工业品牌旗舰系列"><span class="imgmid"></span></div>
											<div class="title">联系地址：</div>
										</div>
										<div class="txt">浙江省杭州市余杭区中泰工业区甲楠智创中心A座5层</div>
									</li>
									<li>
										<div class="head">
											<div class="icon"><img src="/static/picture/icon_contact_mail.png" alt="“至璨/ZHICAN”工业品牌旗舰系列"><span class="imgmid"></span></div>
											<div class="title">联系邮箱：</div>
										</div>
										<div class="txt"><EMAIL></div>
									</li>
									<li>
										<div class="head">
											<div class="icon"><img src="/static/picture/icon_contact_fax.png" alt="“至璨/ZHICAN”工业品牌旗舰系列"><span class="imgmid"></span></div>
											<div class="title">公司传真：</div>
										</div>
										<div class="txt">公司传真</div>
									</li>
									<li>
										<div class="head">
											<div class="icon"><img src="/static/picture/icon_contact_info.png" alt="“至璨/ZHICAN”工业品牌旗舰系列"><span class="imgmid"></span></div>
											<div class="title">售前咨询：</div>
										</div>
										<div class="txt">0571-88640980</div>
									</li>
								</ul>
							</div>
						
   
							<div class="search_form">
							 <form method="get" action="/search.html" class="layui-form">
	        <input type="hidden" name="method" value="1">									<input type="text" name="keywords" id="keys" class="ipt layui-input" placeholder="请输入搜索关键词...">
									<button class="layui-btn" type="submit"><i class="iconfont icon_sousuo"></i>立即搜索</button>
    </form>
							</div>

						</div>
						<div class="menu_list">
							<div class="menu_title">其他咨询服务？</div>
							<div class="items">
												<div class="item">
							<a href="/mianfeishiyong/">
								<div class="icon"><img src="/static/picture/icon_ser_4.png" alt="免费试用"></div>
								<div class="title">免费试用</div>
							</a>
						</div>
												<div class="item">
							<a href="/product/">
								<div class="icon"><img src="/static/picture/icon_ser_2.png" alt="产品中心"></div>
								<div class="title">产品中心</div>
							</a>
						</div>
												<div class="item">
							<a href="/dinggouzixun/">
								<div class="icon"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240103/1-2401031G42TQ.png" alt="订购咨询"></div>
								<div class="title">订购咨询</div>
							</a>
						</div>
						
								
								
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	
	
	</div>
<script type="text/javascript">
	$(document).ready(() => {
		layui.use('form', function() {
			var form = layui.form;
			let c = 'active';
			$('#menuBtn').on('click', function() {
				if(!$(this).hasClass(c)){
					$(this).addClass(c);
					$('#menuList').stop().slideDown(400);
				} else {
					$(this).removeClass(c);
					$('#menuList').stop().slideUp(400);
				}
			});

			//监听提交
			form.on('submit(formDemo)', function(data) {
				layer.msg(JSON.stringify(data.field));
				return false;
			});
		});
	});
	function headerTop(){
		var headerBox = $('.header_yt0516'),
			c = 'active';
		if(!headerBox.hasClass(c)){
			function headerFixe() {
				headerBox.hover(function() {
					headerBox.addClass('on');
				}, function() {
					if(!$('#menuBtn').hasClass(c)){
						headerBox.removeClass('on');
					}
				});
				let top = $(document).scrollTop();
				if(top > 0){
					headerBox.addClass(c);
				} else {
					headerBox.removeClass(c);
				}
			}
			headerFixe();
			$(document).scroll( () => headerFixe() );
		}
		else{
			headerBox.after('<div class="header_nbsp" style="width: 100%; height: ' + headerBox.outerHeight() + 'px;"></div>');
			function isActive(){
				if( $(document).scrollTop() > 0){
					$('#pageSubnav').addClass(c);
				} else {
					$('#pageSubnav').removeClass(c);
				}
			}
			isActive();
			$(document).scroll( () => isActive() );
		}
	}
	headerTop();
	
	
</script>


<!-- 头部 手机版 -->
<div class="head_wap">
	<div class="h_top">
		<a href="https://www.zhaozhongai.com" class="logo">
			<img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240429/1-240429131F1K1.png" alt="“至璨/ZHICAN”工业品牌旗舰系列" title="“至璨/ZHICAN”工业品牌旗舰系列">
		</a>
		<a class="open_nav"><i></i><i></i><i></i></a>
	</div>
	<div class="h_bot">
		<ul class="h_nav">
			<li class="on">
				<div class="a1">
					<div class="top"><a href="https://www.zhaozhongai.com">首页</a></div>
				</div>
			</li>
			
 <li class="">
<div class="a1">
<div class="top"><a href="/solution/">应用案例</a><i class="i1 iconfont icon_arrow-right"></i></div>

<div class="box">
<div class="a2">
<div class="top2"><a href="/cases1/">行业应用案例</a><i class="i2 iconfont icon_arrow-right"></i></div>
</div>
<div class="a2">
<div class="top2"><a href="/cases2/">检测实验应用教学</a><i class="i2 iconfont icon_arrow-right"></i></div>
</div>
<div class="a2">
<div class="top2"><a href="/cases3/">产品拓扑图</a><i class="i2 iconfont icon_arrow-right"></i></div>
</div>


</div>
</div>
</li>
<li class="">
<div class="a1">
<div class="top"><a href="/product/">产品中心</a><i class="i1 iconfont icon_arrow-right"></i></div>

<div class="box">
<div class="a2">
<div class="top2"><a href="/productlist1/">至璨 · CPU单元</a><i class="i2 iconfont icon_arrow-right"></i></div>
</div>
<div class="a2">
<div class="top2"><a href="/productlist2/">至璨 · 总线耦合器</a><i class="i2 iconfont icon_arrow-right"></i></div>
</div>
<div class="a2">
<div class="top2"><a href="/mokuai/">至璨 · IO模块</a><i class="i2 iconfont icon_arrow-right"></i></div>
</div>
<div class="a2">
<div class="top2"><a href="/jiejinchuanganqi/">至璨 · 接近传感器</a><i class="i2 iconfont icon_arrow-right"></i></div>
</div>
<div class="a2">
<div class="top2"><a href="/guangdianchuanganqi/">至璨 · 光电传感器</a><i class="i2 iconfont icon_arrow-right"></i></div>
</div>
<div class="a2">
<div class="top2"><a href="/-chong--lu-juan----hong--hu-dian-/">至璨 · 继电器</a><i class="i2 iconfont icon_arrow-right"></i></div>
</div>
<div class="a2">
<div class="top2"><a href="/jiguangguangdianchuanganqi/">至璨 · 激光光电传感器</a><i class="i2 iconfont icon_arrow-right"></i></div>
</div>
<div class="a2">
<div class="top2"><a href="/jiguangweiyichuanganqi/">至璨 · 激光位移传感器</a><i class="i2 iconfont icon_arrow-right"></i></div>
</div>
<div class="a2">
<div class="top2"><a href="/sebiaochuanganqi/">至璨 · 色标传感器</a><i class="i2 iconfont icon_arrow-right"></i></div>
</div>
<div class="a2">
<div class="top2"><a href="/guangxianchuanganqi/">至璨 · 光纤传感器</a><i class="i2 iconfont icon_arrow-right"></i></div>
</div>
<div class="a2">
<div class="top2"><a href="/-chong--lu-huan--liao---/">至璨 · 微动/行程/限位开关</a><i class="i2 iconfont icon_arrow-right"></i></div>
</div>
<div class="a2">
<div class="top2"><a href="/-chong--lu-bing--liao-donghuan-----/">至璨 · 管道液位传感器</a><i class="i2 iconfont icon_arrow-right"></i></div>
</div>


</div>
</div>
</li>
<li class="">
<div class="a1">
<div class="top"><a href="/service/">服务支持</a><i class="i1 iconfont icon_arrow-right"></i></div>

<div class="box">
<div class="a2">
<div class="top2"><a href="/download/">资料下载</a><i class="i2 iconfont icon_arrow-right"></i></div>
</div>
<div class="a2">
<div class="top2"><a href="/fuwujieshao/">服务介绍</a><i class="i2 iconfont icon_arrow-right"></i></div>
</div>
<div class="a2">
<div class="top2"><a href="/dinggouzixun/">订购咨询</a><i class="i2 iconfont icon_arrow-right"></i></div>
</div>
<div class="a2">
<div class="top2"><a href="/mianfeishiyong/">免费试用</a><i class="i2 iconfont icon_arrow-right"></i></div>
</div>
<div class="a2">
<div class="top2"><a href="/changjianwenti/">常见问题</a><i class="i2 iconfont icon_arrow-right"></i></div>
</div>
<div class="a2">
<div class="top2"><a href="/zaixianliuyan/">在线留言</a><i class="i2 iconfont icon_arrow-right"></i></div>
</div>


</div>
</div>
</li>
<li class="">
<div class="a1">
<div class="top"><a href="/make/">研发制造</a><i class="i1 iconfont icon_arrow-right"></i></div>

<div class="box">
<div class="a2">
<div class="top2"><a href="/yanfashili/">研发实力</a><i class="i2 iconfont icon_arrow-right"></i></div>
</div>
<div class="a2">
<div class="top2"><a href="/kehudingzhi/">客户定制</a><i class="i2 iconfont icon_arrow-right"></i></div>
</div>
<div class="a2">
<div class="top2"><a href="/jingmizhizao/">精密制造</a><i class="i2 iconfont icon_arrow-right"></i></div>
</div>
<div class="a2">
<div class="top2"><a href="/pinzhibaozhang/">品质保障</a><i class="i2 iconfont icon_arrow-right"></i></div>
</div>


</div>
</div>
</li>
<li class="">
<div class="a1">
<div class="top"><a href="/about/">关于我们</a><i class="i1 iconfont icon_arrow-right"></i></div>

<div class="box">
<div class="a2">
<div class="top2"><a href="/abouts/">公司简介</a><i class="i2 iconfont icon_arrow-right"></i></div>
</div>
<div class="a2">
<div class="top2"><a href="/qiyewenhua/">企业文化</a><i class="i2 iconfont icon_arrow-right"></i></div>
</div>
<div class="a2">
<div class="top2"><a href="/fazhanlicheng/">发展历程</a><i class="i2 iconfont icon_arrow-right"></i></div>
</div>
<div class="a2">
<div class="top2"><a href="/zuzhijiagou/">组织架构</a><i class="i2 iconfont icon_arrow-right"></i></div>
</div>
<div class="a2">
<div class="top2"><a href="/join/">招贤纳士</a><i class="i2 iconfont icon_arrow-right"></i></div>
</div>
<div class="a2">
<div class="top2"><a href="/News/"> 新闻中心</a><i class="i2 iconfont icon_arrow-right"></i></div>
</div>


</div>
</div>
</li>
<li class="">
<div class="a1">
<div class="top"><a href="/contact1/">联系我们</a><i class="i1 iconfont icon_arrow-right"></i></div>

<div class="box">
<div class="a2">
<div class="top2"><a href="/contact/">联系方式</a><i class="i2 iconfont icon_arrow-right"></i></div>
</div>
<div class="a2">
<div class="top2"><a href="/guestbook/">在线留言</a><i class="i2 iconfont icon_arrow-right"></i></div>
</div>


</div>
</div>
</li>
	
		</ul>
		
		<div class="h_lan">
					</div>
	</div>
</div>
<div class="header_d"></div>
<script>
	// 移动端
	$('.open_nav').click(function() {
		$(this).toggleClass('on');
		$('.h_bot').slideToggle();
	})

	$('.h_bot .a1 .i1').click(function() {
		$(this).parents('.top').toggleClass('on');
		$(this).parents('.a1').find('.box').slideToggle();
		$(this).parents('li').siblings().removeClass('on').find('.top').removeClass('on');
		$(this).parents('li').siblings().find('.box').slideUp();
		$(this).parents('li').siblings().find('.top2').removeClass('on');
		$(this).parents('li').siblings().find('.box2').slideUp();
	})
	$('.h_bot .a2 .i2').click(function() {
		$(this).parents('.top2').toggleClass('on');
		$(this).parents('.a2').find('.box2').slideToggle();
		$(this).parents('.a2').siblings().find('.top2').removeClass('on');
		$(this).parents('.a2').siblings().find('.box2').slideUp();
	})
</script>

</div>





	<!-- 内页 banner -->
 		<div class="page_banner">
		<div class="banner_main">
			<div class="center w1400">
				<div class="info active">
					<div class="title wow fadeInUp" style="visibility: hidden; animation-name: inherit;">至璨 · CPU单元</div>					<div class="banner_nav wow fadeInUp" style="visibility: hidden; animation-name: inherit;">
						<ul>
						
																	<li class=""><a href="/cpudanyuan/">CPU单元</a></li>
																	
						</ul>
					</div>
					<div class="navigation wow fadeInUp animated" style="visibility: hidden; animation-name: inherit;">
					 <a href="/" class=" ">首页</a> &gt; <a href="/product/" class=" ">产品中心</a> &gt; <a href="/productlist1/">至璨 · CPU单元</a>					</div>
				</div>
			</div>
		</div>
				<div class="img">
		    <img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240328/1-24032Q5411J33.png" alt="至璨 · CPU单元" title="至璨 · CPU单元" class="pc">
		    <img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240328/1-24032Q5411J33.png" title="至璨 · CPU单元" alt="" class="wap">
		</div>
			</div>
	
	<!-- 子导航 -->
	<div class="anchor_nav active" style="position: fixed; top: 90px;">
		<div class="center w1400">
			<div class="alert_btn">
				<div class="title" id="alertBtn">至璨 · CPU单元<div class="icon"><i class="iconfont icon_arrow-down"></i></div></div>				<div class="alert_nav">
					<ul>
	
						  							<li class=""><a href="/cpudanyuan/">CPU单元</a></li>
							
					</ul>
				</div>
			</div>
			<div class="navigation" style="width:65%;position: relative;display:flex;justify-content: flex-end;">
			    <div class="search_form wap_hide fr" style="display: inline-block; margin-right: 30px; width: 45%; margin-top: 10px;">
    <form method="get" action="/search.html" class="layui-form">
	        <input type="hidden" name="method" value="1">									
	        <input type="text" name="keywords" id="keys" style="width:60%;display:inline-block;" class="ipt layui-input fl" placeholder="请输入搜索关键词...">
		   <button class="layui-btn fl" style="background-color:#2b37d8" type="submit"><i style="color:#fff;font-size:20px" class="iconfont icon_sousuo"></i>立即搜索</button>
        <input type="hidden" name="method" value="1"><input type="hidden" name="type" id="type" value="sonself">    </form>
							</div>
				<div class="position fr wap_hide">
				    
				     <a href="/" class=" ">首页</a> &gt; <a href="/product/" class=" ">产品中心</a> &gt; <a href="/productlist1/">至璨 · CPU单元</a>				</div>
			</div>
		</div>
	</div>
	<style> 
	 .navigation{font-size:15px !important;color:#999}
	 .position a{max-width:150px;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;}
	</style>
	<div class="anchor_nbsp"></div>
	<script type="text/javascript">
		$(document).ready(() => {
			function anchorPage() {
				let anchorNav = $('.anchor_nav'),
					w_top = $(document).scrollTop(),
					top = anchorNav.offset().top,
				 	w = $(window).innerWidth(),
					c = 'active',
					h = (w > 990) ? '90' : '60';

				function isShow(e) {
					if(e > top - h) {
						
						anchorNav.addClass(c).css({'position': 'fixed', 'top': h + 'px'});
						$('.anchor_nav .search_form').css({'margin-top':'10px'});
					}else{
					    $('.anchor_nav .search_form').css({'margin-top':'20px'});
						anchorNav.removeClass(c).css({'position': '', 'top': ''});
					}
				}
				isShow(w_top);
				$(document).scroll(function() {
					isShow($(this).scrollTop());
				});
				$('#alertBtn').click(function() {
					$(this).siblings().stop().slideToggle(400);
				});
				
			}
			anchorPage();
		});
	</script>

	
	<div class="anchor_nbsp"></div>
	<div class="filter_box wow fadeInUp" style="visibility: hidden; animation-name: inherit;">
			
	</div>
	<style>
	.filter_one a.active{
	color: #2b37d8 !important;
	}
	.filter_one a.active:after{
	background: #2b37d8;opacity: 1 !important;
	}
	</style>
	<script type="text/javascript">
		$(document).ready(() => {
			if(window.innerWidth <= 990 ){
				$('#filterList').hide();
				$('#isShowBtn').addClass('active');
			}
			$('#isShowBtn').click(function() {
				if(!$(this).hasClass('active')){
					$(this).addClass('active');
					$('#filterList').stop().slideUp(500);
				} else {
					$(this).removeClass('active');
					$('#filterList').stop().slideDown(500);
				}
			});
			function anchorPage() {
				let anchorNav = $('.anchor_nav'),
					w_top = $(document).scrollTop(),
					top = anchorNav.offset().top,
				 	w = $(window).innerWidth(),
					c = 'active',
					h = (w > 990) ? '90' : '60';

				function isShow(e) {
					if(e > top) {
						anchorNav.addClass(c).css({'position': 'fixed', 'top': h + 'px'});
						// $('#sereenList').stop().slideUp(500);
					}else{
						anchorNav.removeClass(c).css({'position': '', 'top': ''});
					}
				}
				isShow(w_top);
				$(document).scroll(function() {
					isShow($(this).scrollTop());
				});
				// $('#alertBtn').click(function() {
				// 	$(this).siblings().stop().slideToggle(400);
				// });
				
				// $('#alertBtn2').click(function() {
				// 	$(this).siblings().stop().slideToggle(400);
				// });
			}
			anchorPage();
		});
	</script>

	<!-- 产品列表 -->
	<div class="cplb_yt0609">
		<div class="cplb_main w1400">
			<div class="cplb_list">
				<ul id="view_product">
										<li class="wow fadeInUp" style="visibility: hidden; animation-name: inherit;"> 
					<div class="icon"><i class="iconfont icon_jiantou"></i></div> 
						<div class="img">
						 <a href="/product/241.html">
							<div class="pb"><div class="ab">
							<img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240507/1-24050G63G9330.png" alt="NX6-1210-CPU116-E" title="NX6-1210-CPU116-E">
							<span class="imgmid"></span>
							</div>
							</div>
						 </a>
						</div> 
					<div class="info"> 
					<div class="en"><a href="/product/241.html">NX6-1210-CPU116-E</a></div> 
						<div class="title">CPU单元</div> 
						<div class="content"> 
						<p style="font-size: medium; white-space: normal;">
						<font face="宋体" style="font-size: 16px;"><span style="font-family: arial, helvetica, sans-serif; font-size: 16px;"></span></font>
						</p>
						<p style="font-size: medium; white-space: normal; text-align: left;"><span style="font-size: 16px; font-family: 微软雅黑, " microsoft=""><font face="宋体"></font></span></p>
						<p style="font-size: medium;white-space: normal"><span style="font-family: arial, helvetica, sans-serif; font-size: 16px;"><font face="宋体"></font></span></p>
						
							<p>性能强悍、多样兼容、<span style="text-wrap: wrap;">轻松编程</span></p><p>接口多样、<span style="text-wrap: wrap;">32轴控制、</span>易于扩展</p>						
						</div> 
						<div class="more"> 
												<div class="item"> 
							<a href="/download/"> 
								<div class="link"> 
								<img src="/static/images/icon_ml.png" alt="NX6-1210-CPU116-E"> <img src="/static/images/icon_ml_active.png" alt="NX6-1210-CPU116-E" class="active"> 
								</div> 
								<div class="txt">资料下载</div> 
							</a> 
						</div> 
												<div class="item"> 
						 <a href="/dinggouzixun/"> 
							 <div class="link"> 
								<img src="/static/images/icon_bj.png" alt="NX6-1210-CPU116-E"> 
								<img src="/static/images/icon_bj_active.png" alt="NX6-1210-CPU116-E" class="active"> 
							 </div> 
							<div class="txt">报价</div> 
						 </a> 
						</div> 
												</div> 
					</div> 
					</li>
									</ul>
			</div>
			<div id="pageview_product">
					<div class="layui-box layui-laypage layui-laypage-default" id="layui-laypage-1">
					<ul>	 共<strong>1</strong>页 <strong>1</strong>条</ul>
					</div>
			</div>
		</div>
	</div>
	<style>
	    #pageview_product ul li{display: inline-block;border:1px solid rgba(0,0,0,0.1)}
	    #pageview_product ul li.active{background:#2b37d8;}
	    #pageview_product ul li.active a{color:#fff !important;background:#2b37d8;}
	</style>
 
	<div class="page_service_yt0606">
		<div class="service_main w1400">
			<div class="page_title wow fadeInUp" style="visibility: hidden; animation-name: inherit;"><span datatype="span" class="">您需要帮助与服务吗？</span></div>
			<div class="service_list" id="service">
				<ul>
									<li class="wow fadeInUp hrefall" style="visibility: hidden; animation-name: inherit;">
						<a href="/contact1/" class="hrefson">
							<div class="more"><i class="iconfont icon_jiantou"></i></div>
							<div class="number" datatype="span">01.</div>
							<div class="info">
								<div class="title" datatype="hrefs">联系我们</div>
								<div class="txt" datatype="span">DISCONTINUED REPLACEMENT</div>
							</div>
							<div class="move"></div>
							<div class="icon">
								<img datatype="img" src="/static/picture/page_icon_ser_5.png" alt="联系我们">
								<img datatype="img" src="/static/picture/page_icon_ser_5_active.png" alt="" class="active" title="">
							</div>
						</a>
					</li>
								
					<li class="wow fadeInUp hrefall" style="visibility: hidden; animation-name: inherit;">
						<a href="javascript:;" class="hrefson kf">
							<div class="more"><i class="iconfont icon_jiantou"></i></div>
							<div class="number" datatype="span">02.</div>
							<div class="info">
								<div class="title" datatype="hrefs">在线咨询</div>
								<div class="txt" datatype="span">ONLINE CONSULTATION</div>
							</div>
							<div class="move"></div>
							<div class="icon">
								<img datatype="img" src="/static/picture/page_icon_ser_2.png" alt="在线咨询">
								<img datatype="img" src="/static/picture/page_icon_ser_2_active.png" alt="在线咨询" class="active">
							</div>
						</a>
					</li>
				
									<li class="wow fadeInUp hrefall" style="visibility: hidden; animation-name: inherit;">
						
						<a href="/dinggouzixun/" class="hrefson">
							<div class="more"><i class="iconfont icon_jiantou"></i></div>
							<div class="number" datatype="span">03.</div>
							<div class="info">
								<div class="title eait_View" datatype="hrefs">价格垂询</div>
								<div class="txt" datatype="span">PRICE INQUIRY</div>
							</div>
							<div class="move"></div>
							<div class="icon">
								<img datatype="img" src="/static/picture/page_icon_ser_7.png" alt="价格垂询">
								<img datatype="img" src="/static/picture/page_icon_ser_7_active.png" alt="价格垂询" class="active">
							</div>
						</a>
					</li>
									<li class="wow fadeInUp hrefall" style="visibility: hidden; animation-name: inherit;">
						<a href="/product/" class="hrefson">
							<div class="more"><i class="iconfont icon_jiantou"></i></div>
							<div class="number" datatype="span">04.</div>
							<div class="info">
								<div class="title" datatype="hrefs">快速选型</div>
								<div class="txt" datatype="span">QUICK SELECTION</div>
							</div>
							<div class="move"></div>
							<div class="icon">
								<img datatype="img" src="/static/picture/page_icon_ser_8.png" alt="快速选型">
								<img datatype="img" src="/static/picture/page_icon_ser_8_active.png" alt="快速选型" class="active">
							</div>
						</a>
					</li>
					
				</ul>
			</div>
			<div class="cle"></div>
		</div>
	</div>
	
 

	<div class="bjall">
		<div class="idx_advert_yt0520 bj2" datatype="bj" data_img="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240108/1-24010QS635U0.jpg" style="background: url(//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240108/1-24010QS635U0.jpg) no-repeat center / cover;">
			<div class="advert_bg" datatype="span">ZHAOZHONG</div>
			<div class="advert_mian w1600">
				<div class="advert_info wow fadeInUp" style="visibility: visible; animation-name: fadeInUp;">
					<div class="title" datatype="span">快速交付与全面支持</div>
					<div class="txt" datatype="span">兆众智能通过现场操作指导和售后技术支持，为客户提供从产品选择到生产线运行的全程支持。</div>
				</div>
				<div class="advert_items wow fadeInUp" style="visibility: visible; animation-name: fadeInUp;">
					<div class="advert_one">
					<div class="title" datatype="span">服务支持</div>						<div class="more active hrefall">
							<a href="/ziliaoxiazai1/" class="hrefson">
								<div class="txt" datatype="hrefs">资料下载中心</div>
								<div class="icon"><i></i><i></i></div>
							</a>
						</div>
						
					</div>
										<div class="advert_one">
						<div class="title" datatype="span">免费试用</div>
						<div class="more active hrefall">
							<a href="/mianfeishiyong/" class="hrefson">
								<div class="txt" datatype="hrefs">申请免费试用</div>
								<div class="icon"><i></i><i></i></div>
							</a>
						</div>
					</div>
									</div>
			</div>
		</div>
	</div>
<script>

$(".head_wap > .h_bot > .h_nav > li:eq(0)").addClass('on').siblings().removeClass('on');
</script>
<div id="footer">
    <!-- 底部 -->
<div class="footer_yt0520">
	<div class="footer_main w1600">
		<div class="footer_head">
					<div class="footer_title wow fadeInUp" style="visibility: visible; animation-name: fadeInUp;">
				<div class="title"><span>CONTACT US</span></div>
				<div class="slogan">
					<p>我们以可靠品质成就客户<em>，</em><br>以优秀服务赢得市场<em>。</em></p></div>
			</div>
			
			<div class="footer_nav wow fadeInUp" style="visibility: visible; animation-name: fadeInUp;">
				<ul>
									<li>
						<div class="title"><a href="/solution/">应用案例</a></div>
						<div class="menu">
													<a href="/cases1/">行业应用案例</a>
													<a href="/cases2/">检测实验应用教学</a>
													<a href="/cases3/">产品拓扑图</a>
												</div>
					</li>
									<li>
						<div class="title"><a href="/service/">服务支持</a></div>
						<div class="menu">
													<a href="/download/">资料下载</a>
													<a href="/fuwujieshao/">服务介绍</a>
													<a href="/dinggouzixun/">订购咨询</a>
													<a href="/mianfeishiyong/">免费试用</a>
													<a href="/changjianwenti/">常见问题</a>
													<a href="/zaixianliuyan/">在线留言</a>
												</div>
					</li>
									<li>
						<div class="title"><a href="/make/">研发制造</a></div>
						<div class="menu">
													<a href="/yanfashili/">研发实力</a>
													<a href="/kehudingzhi/">客户定制</a>
													<a href="/jingmizhizao/">精密制造</a>
													<a href="/pinzhibaozhang/">品质保障</a>
												</div>
					</li>
									<li>
						<div class="title"><a href="/about/">关于我们</a></div>
						<div class="menu">
													<a href="/abouts/">公司简介</a>
													<a href="/qiyewenhua/">企业文化</a>
													<a href="/fazhanlicheng/">发展历程</a>
													<a href="/zuzhijiagou/">组织架构</a>
													<a href="/join/">招贤纳士</a>
													<a href="/News/"> 新闻中心</a>
												</div>
					</li>
								</ul>
			</div>
			<div class="qrcode">
				<!--<div class="one">-->
				<!--	<div class="img"><img src="static/picture/64fbd41c2bb08.png" alt="BOJKE博亿精科"></div>-->
				<!--	<div class="title">微信在线客服</div>-->
				<!--</div>-->
				<div class="one">
					<div class="img"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240226/1-240226111319411.jpg" alt="微信公众号"></div>
					<div class="title">微信公众号</div>
				</div>
			</div>
		</div>
		<div class="footer_body wow fadeInUp" style="visibility: visible; animation-name: fadeInUp;">
			<div class="info">
				<div class="contact">
					<div class="one">
						<div class="icon"><img src="/static/picture/icon_mail.png" alt="联系邮箱"></div>
						<div class="txt"><EMAIL></div>
					</div>
					<div class="one">
						<div class="icon"><img src="/static/picture/icon_phone.png" alt="售前咨询"></div>
						<div class="txt">0571-88640980</div>
					</div>
					<div class="one" style="width: 100%;">
						<div class="icon"><img src="/static/picture/icon_address.png" alt="联系地址"></div>
						<div class="txt">浙江省杭州市余杭区中泰工业区甲楠智创中心A座5层</div>
					</div>
				</div>
				<div class="copy pc">Copyright © 2024 浙江兆众智能电子科技有限公司 
All Rights Reserved　版权所有　<a href="https://beian.miit.gov.cn/" rel="nofollow" target="_blank">浙ICP备17057674号-2</a>　　			  　　<a href="/yinsitiaokuan/">隐私条款</a>　　
			     			  　　<a href="/falvshengming/">法律声明</a>　　
			      
			  </div>
				<div class="copy mobile">
				    <p>Copyright © 2024 浙江兆众智能电子科技有限公司 
All Rights Reserved　版权所有</p>
				    <p><a href="https://beian.miit.gov.cn/" rel="nofollow" target="_blank">浙ICP备17057674号-2</a>　　</p>
				    <p>
										　　<a href="javascript:;">隐私条款</a>　　
					 					　　<a href="javascript:;">法律声明</a>　　
					  
					</p>
				</div>
			</div>
			<div class="qrcode">
				<!--<div class="one">-->
				<!--	<div class="img"><img src="static/picture/64fbd41c2bb08.png" alt="BOJKE博亿精科"></div>-->
				<!--	<div class="title">微信在线客服</div>-->
				<!--</div>-->
				<div class="one">
					<div class="img"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20240226/1-240226111319411.jpg" alt="微信公众号"></div>
					<div class="title">微信公众号</div>
				</div>
			</div>
		</div>
	</div>
</div>
<script type="text/javascript">
	const wow = new WOW({
		boxClass: 'wow', //需要执行动画的元素的 class
		animateClass: 'animated', //animation.css 动画的 class
		offset: 0, //距离可视区域多少开始执行动画
		mobile: false, //是否在移动设备上执行动画
		live: true //异步加载的内容是否有效
	})
	wow.init()
</script>

<!-- 客服挂件 -->
<div class="pendant_box">
	<div class="zixun_link">
			<a href="/mianfeishiyong/">
			<div class="icon"><i class="iconfont icon_kefu"></i></div>
			<div class="title">免费试用</div>
		</a>
		
	</div>
	<div class="zixun_hover">
		<!-- 微信咨询 -->
		<div class="item wx_zixun">
			<div class="head">
				<div class="icon"><i class="iconfont icon_erweima"></i></div>
				<div class="title">微信咨询</div>
			</div>
			<div class="info">
				<div class="img"><img src="//zhaozhongai.oss-accelerate.aliyuncs.com/uploads/allimg/20250611/1-2506111046123Y.png" alt="微信咨询"></div>
				<div class="txt">扫一扫添加微信</div>
			</div>
		</div>
		<div class="line"></div>
		<!-- 电话咨询 -->
		<div class="item phone_zixun">
			<div class="head">
				<div class="icon"><i class="iconfont icon_kefu"></i></div>
				<div class="title">电话咨询</div>
			</div>
			<div class="info">
				<div class="info_li">
					<div class="title">
						<div class="icon"><img src="static/picture/icon_dh.png" alt="售前咨询"></div>
						<div class="txt">
							<div class="b1">售前咨询</div>
							<div class="b2">0571-88640980</div>
						</div>
					</div>
				</div>
				<div class="info_li">
					<div class="title">
						<div class="icon"><img src="/static/picture/icon_lx.png" alt="我们联系您"></div>
						<div class="txt">
							<div class="b1">我们联系您</div>
						</div>
					</div>
					<div class="form">
    <form method="POST" enctype="multipart/form-data" action="/index.php?m=home&amp;c=Lists&amp;a=gbook_submit&amp;lang=cn" onsubmit="return submit390c99f735c5ee48e82d8974d5849329(this);" class="layui-form">
							<input type="text" class="layui-input float2" id="con_phone" name="输入手机号" placeholder="输入手机号">
							<button class="layui-btn" type="submit">立即提交</button>
        <input type="hidden" name="gourl" id="gourl_390c99f735c5ee48e82d8974d5849329" value="https%3A%2F%2Fwww.zhaozhongai.com%2Fproductlist1%2F"><input type="hidden" name="typeid" value="47"><input type="hidden" name="__token__390c99f735c5ee48e82d8974d5849329" id="390c99f735c5ee48e82d8974d5849329" value="af12e4bc27dbadcb749caff777694ae6"><input type="hidden" name="form_type" value="0"><script type="text/javascript">
    function submit390c99f735c5ee48e82d8974d5849329(elements)
    {
        if (document.getElementById('gourl_390c99f735c5ee48e82d8974d5849329')) {
            document.getElementById('gourl_390c99f735c5ee48e82d8974d5849329').value = encodeURIComponent(window.location.href);
        }
            var x = elements;
    for (var i=0;i<x.length;i++) {
        
                            if(x[i].name == 'attr_34' && x[i].value.length == 0){
                                alert('输入手机号不能为空！');
                                return false;
                            }
                         
                    if(x[i].name == 'attr_34' && !(/^([\d\-\+]+)$/.test( x[i].value)) && x[i].value.length > 0){
                        alert('输入手机号格式不正确！！');
                        return false;
                    }
                   
    }
        
        elements.submit();
    }

    function ey_fleshVerify_1754028260(id)
    {
        var token = id.replace(/verify_/g, '__token__');
        var src = "/index.php?m=api&c=Ajax&a=vertify&type=guestbook&lang=cn&token="+token;
        src += "&r="+ Math.floor(Math.random()*100);
        document.getElementById(id).src = src;
    }

    function f7874bf57607a4d75ef99cc2a22872489()
    {
        var ajax = new XMLHttpRequest();
        ajax.open("post", "/index.php?m=api&c=Ajax&a=get_token", true);
        ajax.setRequestHeader("X-Requested-With","XMLHttpRequest");
        ajax.setRequestHeader("Content-type","application/x-www-form-urlencoded");
        ajax.send("name=__token__390c99f735c5ee48e82d8974d5849329");
        ajax.onreadystatechange = function () {
            if (ajax.readyState==4 && ajax.status==200) {
                document.getElementById("390c99f735c5ee48e82d8974d5849329").value = ajax.responseText;
                document.getElementById("gourl_390c99f735c5ee48e82d8974d5849329").value = encodeURIComponent(window.location.href);
          　}
        } 
    }
    f7874bf57607a4d75ef99cc2a22872489();
    function getNext1598839807(id,name,level) {
        var input = document.getElementById('attr_'+name);
        var first = document.getElementById('first_id_'+name);
        var second = document.getElementById('second_id_'+name);
        var third = document.getElementById('third_id_'+name);
        var findex ='', fvalue = '',sindex = '',svalue = '',tindex = '',tvalue = '',value='';

        if (level == 1){
            if (second) {
                second.style.display = 'none';
                second.innerHTML  = ''; 
            }
            if (third) {
                third.style.display = 'none';
                third.innerHTML  = '';
            }
            findex = first.selectedIndex;
            fvalue = first.options[findex].value;
            input.value = fvalue;
            value = fvalue;
        } else if (level == 2){
            if (third) {
                third.style.display = 'none';
                third.innerHTML  = '';
            }
            findex = first.selectedIndex;
            fvalue = first.options[findex].value;
            sindex = second.selectedIndex;
            svalue = second.options[sindex].value;
            if (svalue) {
                input.value = fvalue+','+svalue;
                value = svalue;
            }else{
                input.value = fvalue;
            }
        } else if (level == 3){
            findex = first.selectedIndex;
            fvalue = first.options[findex].value;
            sindex = second.selectedIndex;
            svalue = second.options[sindex].value;
            tindex = third.selectedIndex;
            tvalue = third.options[tindex].value;
            if (tvalue) {
                input.value = fvalue+','+svalue+','+tvalue;
                value = tvalue;
            }else{
                input.value = fvalue+','+svalue;
            }
        } 
        if (value) {
            if(document.getElementById(id))
            {
                document.getElementById(id).options.add(new Option('请选择','')); 
                var ajax = new XMLHttpRequest();
                ajax.open("post", "/index.php?m=api&c=Ajax&a=get_region", true);
                ajax.setRequestHeader("X-Requested-With","XMLHttpRequest");
                ajax.setRequestHeader("Content-type","application/x-www-form-urlencoded");
                ajax.send("pid="+value);
                ajax.onreadystatechange = function () {
                    if (ajax.readyState==4 && ajax.status==200) {
                        var data = JSON.parse(ajax.responseText).data;
                        if (data) {
                            data.forEach(function(item) {
                                document.getElementById(id).options.add(new Option(item.name,item.id)); 
                                document.getElementById(id).style.display = "block";
                            });
                        }
                  　}
                }
            }
        }
    }
</script>    </form>
					</div>
				</div>
			</div>
		</div>
	</div>
	<div class="scroll_top" id="scrollTopBtn" style="display: block;"><i class="iconfont icon_icon_top"></i></div>
</div>
<script type="text/javascript">
	$(document).ready(function() {
		function clickScrollTop(){
			var btn = $('#scrollTopBtn');
			function isShow(){
				if($(document).scrollTop() > 100){
					btn.stop().fadeIn(300);
				} else {
					btn.stop().fadeOut(300);
				}
			}
			isShow();
			$(document).scroll(function(){ isShow() });
			btn.click(function(){
				$('html, body').animate({scrollTop: 0}, 500);
			});
		}
		clickScrollTop();
	});
</script>
</div>

<script type="text/javascript" src="/static/js/form.js"></script>
<script type="text/javascript" src="/static/js/footer.js"></script>
<script language="javascript" type="text/javascript" src="/template/default/pc/static/js/pages.js"></script>
<script>
    $(".nav li:last a").removeClass("active");
</script>
<script>
var _hmt = _hmt || [];
(function() {
  var hm = document.createElement("script");
  hm.src = "https://hm.baidu.com/hm.js?71717ad93e343f6e3d55e6a8e630767f";
  var s = document.getElementsByTagName("script")[0]; 
  s.parentNode.insertBefore(hm, s);
})();
</script>

<script type="text/javascript">var root_dir="";var ey_aid=0;</script>
<script language="javascript" type="text/javascript" src="/public/static/common/js/ey_footer.js?v=v1.7.4"></script>

 

</body></html>