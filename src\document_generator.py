import logging
import pandas as pd
from pathlib import Path
from typing import List, Dict, Any
from datetime import datetime

logger = logging.getLogger(__name__)

class DocumentGenerator:
    def __init__(self, products_data: List[Dict[str, Any]], categories: Dict[str, Any], technical_specs: Dict[str, Any]):
        self.products_data = products_data
        self.categories = categories
        self.technical_specs = technical_specs
        
    async def generate_all_documents(self, output_dir: Path):
        logger.info("开始生成销售支持文档...")
        
        await self.generate_product_catalog(output_dir)
        await self.generate_customer_matching_guide(output_dir)
        await self.generate_product_recommendation_table(output_dir)
        await self.generate_sales_script_library(output_dir)
        
        logger.info("所有销售支持文档生成完成！")
        
    async def generate_product_catalog(self, output_dir: Path):
        logger.info("生成产品分类手册...")
        
        catalog_content = []
        catalog_content.append("# 赵中AI产品分类手册")
        catalog_content.append(f"\n生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        catalog_content.append(f"产品总数: {len(self.products_data)}")
        catalog_content.append(f"分类总数: {len(self.categories)}")
        
        for category_name, category_data in self.categories.items():
            catalog_content.append(f"\n## {category_name}")
            catalog_content.append(f"产品数量: {category_data['count']}")
            
            for product in category_data['products']:
                catalog_content.append(f"\n### {product['name']}")
                if product.get('description'):
                    catalog_content.append(f"描述: {product['description']}")
                
                specs = product.get('technical_specs', {})
                if specs:
                    catalog_content.append("关键参数:")
                    for spec_name, spec_value in list(specs.items())[:3]:
                        catalog_content.append(f"- {spec_name}: {spec_value}")
                
                applications = product.get('applications', [])
                if applications:
                    catalog_content.append(f"应用场景: {', '.join(applications[:3])}")
                
        catalog_file = output_dir / "产品分类手册.md"
        with open(catalog_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(catalog_content))
            
        logger.info(f"产品分类手册已保存: {catalog_file}")
        
    async def generate_customer_matching_guide(self, output_dir: Path):
        logger.info("生成客户需求匹配指南...")
        
        guide_content = []
        guide_content.append("# 客户需求快速匹配指南")
        guide_content.append(f"\n生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        guide_content.append("\n## 三步确认法")
        guide_content.append("### 第一步：确认检测对象")
        guide_content.append("- 您需要检测什么？（温度、湿度、压力、距离、气体等）")
        
        guide_content.append("\n### 第二步：确认检测距离和范围")
        guide_content.append("- 检测距离是多少？（接触式、近距离、远距离）")
        guide_content.append("- 精度要求如何？（±1%、±0.1%等）")
        
        guide_content.append("\n### 第三步：确认特殊要求")
        guide_content.append("- 工作环境条件？（温度、湿度、防护等级）")
        guide_content.append("- 输出信号要求？（模拟量、数字量、通信协议）")
        
        guide_file = output_dir / "客户需求快速匹配指南.md"
        with open(guide_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(guide_content))
            
        logger.info(f"客户需求匹配指南已保存: {guide_file}")
        
    async def generate_product_recommendation_table(self, output_dir: Path):
        logger.info("生成产品推荐速查表...")
        
        recommendations = []
        
        for product in self.products_data:
            applications = product.get('applications', ['通用应用'])
            
            for app in applications:
                selling_points = product.get('key_selling_points', [])
                key_points = "; ".join(selling_points[:3]) if selling_points else "高性能、稳定可靠"
                
                industries = product.get('target_industries', [])
                industry_str = ", ".join(industries) if industries else "多行业通用"
                
                price_info = product.get('price', {})
                price_range = price_info.get('formatted', '面议')
                
                recommendations.append({
                    '客户需求': app,
                    '推荐产品': product['name'],
                    '核心卖点': key_points,
                    '适用行业': industry_str,
                    '价格区间': price_range,
                    '产品分类': product.get('category', '未分类')
                })
        
        df = pd.DataFrame(recommendations)
        excel_file = output_dir / "产品推荐速查表.xlsx"
        
        with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='产品推荐', index=False)
        
        logger.info(f"产品推荐速查表已保存: {excel_file}")
        
    async def generate_sales_script_library(self, output_dir: Path):
        logger.info("生成销售话术库...")
        
        script_content = []
        script_content.append("# 标准销售话术库")
        script_content.append(f"\n生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        script_content.append("\n## 开场白话术")
        script_content.append("### 开场白 1")
        script_content.append('"您好！我是赵中AI的销售顾问，了解到您对传感器产品有需求，我们有专业的解决方案可以帮助您。"')
        script_content.append("\n**使用场景**: 初次接触客户时使用")
        
        script_content.append("\n## 产品介绍话术")
        script_content.append("### 产品介绍 1")
        script_content.append('"这款产品的主要优势是高精度和稳定性，特别适合工业自动化行业使用。"')
        
        script_content.append("\n## 异议处理话术")
        script_content.append("### 价格太高")
        script_content.append("**回应方式 1**: 我理解您对价格的考虑。但是考虑到产品的高性能和长期使用成本，这个投资是非常值得的。")
        
        script_file = output_dir / "标准销售话术库.md"
        with open(script_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(script_content))
            
        logger.info(f"销售话术库已保存: {script_file}")