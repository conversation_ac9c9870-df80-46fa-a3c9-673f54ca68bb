#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>
@license          http://www.178188.xyz
@lastmodify       2025年8月1日
模块说明: 销售工具文档生成器 - 基于PDF内容的完整实现
"""

import json
import logging
import pandas as pd
from typing import List, Dict, Any, Optional
from pathlib import Path
from datetime import datetime
import re

logger = logging.getLogger(__name__)

class SalesDocumentGenerator:
    """销售工具文档生成器"""

    def __init__(self, products_data: List[Dict[str, Any]]):
        """
        初始化文档生成器

        Args:
            products_data: 从爬虫获取的产品数据列表
        """
        self.products_data = products_data
        self.categories = self._organize_by_category()

    def _organize_by_category(self) -> Dict[str, Dict[str, List[Dict[str, Any]]]]:
        """按分类组织产品数据"""
        categories = {}

        for product in self.products_data:
            category = product.get('category', '未分类')
            subcategory = product.get('subcategory', '其他')

            if category not in categories:
                categories[category] = {}

            if subcategory not in categories[category]:
                categories[category][subcategory] = []

            categories[category][subcategory].append(product)

        return categories

    def _group_by_applications(self) -> Dict[str, List[Dict[str, Any]]]:
        """按应用场景分组产品"""
        application_groups = {}

        for product in self.products_data:
            applications = product.get('applications', ['通用应用'])

            for app in applications:
                if app not in application_groups:
                    application_groups[app] = []
                application_groups[app].append(product)

        return application_groups

    def _extract_key_specs(self, specs: Dict[str, str], max_count: int = 5) -> Dict[str, str]:
        """提取关键技术规格"""
        if not specs:
            return {}

        # 优先级关键词
        priority_keywords = [
            '电压', '电流', '功耗', '精度', '范围', '温度', '湿度',
            '压力', '距离', '频率', '分辨率', '响应时间', '工作温度'
        ]

        key_specs = {}

        # 首先添加包含优先级关键词的规格
        for key, value in specs.items():
            if any(keyword in key for keyword in priority_keywords):
                key_specs[key] = value
                if len(key_specs) >= max_count:
                    break

        # 如果还没达到最大数量，添加其他规格
        if len(key_specs) < max_count:
            for key, value in specs.items():
                if key not in key_specs:
                    key_specs[key] = value
                    if len(key_specs) >= max_count:
                        break

        return key_specs

    def _generate_competitive_advantages(self, product: Dict[str, Any]) -> List[str]:
        """生成竞争优势描述"""
        advantages = []

        # 基于产品特点生成优势
        features = product.get('features', [])
        for feature in features:
            if any(keyword in feature.lower() for keyword in ['高', '精确', '稳定', '可靠', '智能']):
                advantages.append(f"技术优势: {feature}")

        # 基于技术规格生成优势
        specs = product.get('technical_specs', {})
        for key, value in specs.items():
            if '精度' in key and any(char in value for char in ['±', '%']):
                advantages.append(f"精度优势: {key} {value}")
            elif '温度' in key and '-' in value:
                advantages.append(f"环境适应性: 宽温度工作范围 {value}")

        # 基于应用场景生成优势
        applications = product.get('applications', [])
        if len(applications) > 3:
            advantages.append(f"应用广泛: 适用于{len(applications)}个不同应用场景")

        return advantages[:5]  # 最多返回5个优势

    async def generate_all_documents(self, output_dir: Path):
        """生成所有销售支持文档"""
        logger.info("开始生成销售支持文档...")

        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)

        # 生成完整产品目录
        catalog_content = self.generate_complete_catalog()
        catalog_file = output_dir / "完整产品目录.md"
        with open(catalog_file, 'w', encoding='utf-8') as f:
            f.write(catalog_content)
        logger.info(f"完整产品目录已保存: {catalog_file}")

        # 生成销售指南
        sales_guide = self.generate_sales_guide()
        guide_file = output_dir / "销售人员产品推荐指南.md"
        with open(guide_file, 'w', encoding='utf-8') as f:
            f.write(sales_guide)
        logger.info(f"销售指南已保存: {guide_file}")

        # 生成行业特定指南
        industries = ['工业自动化', '智能制造', '环境监测', '建筑智能化']
        for industry in industries:
            industry_guide = self.generate_sales_guide(target_industry=industry)
            industry_file = output_dir / f"{industry}_产品推荐指南.md"
            with open(industry_file, 'w', encoding='utf-8') as f:
                f.write(industry_guide)
            logger.info(f"{industry}指南已保存: {industry_file}")

        # 生成产品对比表
        await self.generate_product_comparison_table(output_dir)

        # 生成客户匹配指南
        await self.generate_customer_matching_guide(output_dir)

        # 生成销售话术库
        await self.generate_sales_script_library(output_dir)

        logger.info("所有销售支持文档生成完成！")

    def generate_complete_catalog(self) -> str:
        """生成完整产品目录"""
        catalog = []
        catalog.append("# 赵中AI产品完整目录\n")
        catalog.append(f"生成时间: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}\n")
        catalog.append(f"产品总数: {len(self.products_data)}\n")
        catalog.append("---\n")

        for category_name, subcategories in self.categories.items():
            catalog.append(f"## {category_name}\n")

            for subcategory_name, products in subcategories.items():
                catalog.append(f"### {subcategory_name}\n")
                catalog.append(f"产品数量: {len(products)}\n")

                for product in products:
                    catalog.append(f"#### {product.get('name', '未知产品')}\n")

                    # 基本信息
                    if product.get('description'):
                        catalog.append(f"**产品描述**: {product['description']}\n")

                    # 技术规格
                    specs = product.get('technical_specs', {})
                    if specs:
                        catalog.append("**技术规格**:\n")
                        for key, value in specs.items():
                            catalog.append(f"- {key}: {value}\n")
                        catalog.append("")

                    # 应用场景
                    applications = product.get('applications', [])
                    if applications:
                        catalog.append(f"**应用场景**: {', '.join(applications)}\n")

                    # 产品特点
                    features = product.get('features', [])
                    if features:
                        catalog.append("**产品特点**:\n")
                        for feature in features:
                            catalog.append(f"- {feature}\n")
                        catalog.append("")

                    # 资料链接
                    if product.get('pdf_url'):
                        catalog.append(f"**技术资料**: [下载PDF]({product['pdf_url']})\n")

                    catalog.append("---\n")

        return "\n".join(catalog)

    def generate_sales_guide(self, target_industry: str = None) -> str:
        """生成销售指南"""
        guide = []
        guide.append("# 销售人员产品推荐指南\n")
        guide.append(f"生成时间: {datetime.now().strftime('%Y年%m月%d日')}\n")

        if target_industry:
            guide.append(f"目标行业: {target_industry}\n")

        guide.append("---\n")

        # 按应用场景分组推荐
        application_groups = self._group_by_applications()

        guide.append("## 按应用场景分类推荐\n")

        for application, products in application_groups.items():
            if target_industry and target_industry.lower() not in application.lower():
                continue

            guide.append(f"### {application}\n")
            guide.append(f"相关产品数量: {len(products)}\n")

            # 推荐前3个最相关的产品
            top_products = products[:3]

            for i, product in enumerate(top_products, 1):
                guide.append(f"#### 推荐产品 {i}: {product.get('name', '未知产品')}\n")

                # 销售卖点
                features = product.get('features', [])
                if features:
                    guide.append("**主要卖点**:\n")
                    for feature in features[:3]:  # 只显示前3个特点
                        guide.append(f"- {feature}\n")
                    guide.append("")

                # 技术优势
                specs = product.get('technical_specs', {})
                key_specs = self._extract_key_specs(specs)
                if key_specs:
                    guide.append("**关键技术参数**:\n")
                    for key, value in key_specs.items():
                        guide.append(f"- {key}: {value}\n")
                    guide.append("")

                # 竞争优势
                advantages = self._generate_competitive_advantages(product)
                if advantages:
                    guide.append("**竞争优势**:\n")
                    for advantage in advantages:
                        guide.append(f"- {advantage}\n")
                    guide.append("")

                guide.append("---\n")

        return "\n".join(guide)

    async def generate_product_comparison_table(self, output_dir: Path):
        """生成产品对比表"""
        logger.info("生成产品对比表...")

        comparison_data = []

        for product in self.products_data:
            # 提取关键信息用于对比
            specs = product.get('technical_specs', {})
            key_specs = self._extract_key_specs(specs, max_count=3)

            applications = product.get('applications', [])
            features = product.get('features', [])

            comparison_data.append({
                '产品名称': product.get('name', '未知产品'),
                '产品分类': product.get('category', '未分类'),
                '子分类': product.get('subcategory', '其他'),
                '主要应用': ', '.join(applications[:3]) if applications else '通用',
                '核心特点': ', '.join(features[:2]) if features else '高性能',
                '关键参数1': list(key_specs.keys())[0] if key_specs else '',
                '参数值1': list(key_specs.values())[0] if key_specs else '',
                '关键参数2': list(key_specs.keys())[1] if len(key_specs) > 1 else '',
                '参数值2': list(key_specs.values())[1] if len(key_specs) > 1 else '',
                '关键参数3': list(key_specs.keys())[2] if len(key_specs) > 2 else '',
                '参数值3': list(key_specs.values())[2] if len(key_specs) > 2 else '',
                '技术资料': product.get('pdf_url', '')
            })

        # 保存为Excel文件
        df = pd.DataFrame(comparison_data)
        excel_file = output_dir / "产品对比表.xlsx"

        with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='产品对比', index=False)

            # 按分类创建不同的工作表
            for category_name, subcategories in self.categories.items():
                category_products = []
                for subcategory_name, products in subcategories.items():
                    for product in products:
                        category_products.append(product)

                if category_products:
                    category_data = [row for row in comparison_data
                                   if row['产品分类'] == category_name]
                    if category_data:
                        category_df = pd.DataFrame(category_data)
                        sheet_name = category_name[:30]  # Excel工作表名称限制
                        category_df.to_excel(writer, sheet_name=sheet_name, index=False)

        logger.info(f"产品对比表已保存: {excel_file}")

    async def generate_customer_matching_guide(self, output_dir: Path):
        """生成客户需求匹配指南"""
        logger.info("生成客户需求匹配指南...")

        guide_content = []
        guide_content.append("# 客户需求快速匹配指南\n")
        guide_content.append(f"生成时间: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}\n")
        guide_content.append("---\n")

        guide_content.append("## 客户需求分析三步法\n")
        guide_content.append("### 第一步：明确检测对象\n")
        guide_content.append("**关键问题**:\n")
        guide_content.append("- 您需要检测什么参数？（温度、湿度、压力、距离、气体浓度等）\n")
        guide_content.append("- 检测对象的特性如何？（固体、液体、气体）\n")
        guide_content.append("- 是否有特殊要求？（防爆、防腐、高温等）\n")

        guide_content.append("### 第二步：确定技术要求\n")
        guide_content.append("**关键问题**:\n")
        guide_content.append("- 检测范围是多少？（量程范围）\n")
        guide_content.append("- 精度要求如何？（±1%、±0.1%等）\n")
        guide_content.append("- 响应时间要求？（实时、秒级、分钟级）\n")
        guide_content.append("- 工作环境条件？（温度范围、湿度、振动等）\n")

        guide_content.append("### 第三步：明确系统集成需求\n")
        guide_content.append("**关键问题**:\n")
        guide_content.append("- 输出信号类型？（4-20mA、0-10V、数字信号）\n")
        guide_content.append("- 通信协议要求？（Modbus、Profibus、以太网等）\n")
        guide_content.append("- 安装方式？（螺纹、法兰、卡箍等）\n")
        guide_content.append("- 供电要求？（24VDC、220VAC等）\n")

        # 基于实际产品数据生成匹配建议
        guide_content.append("## 常见需求快速匹配\n")

        application_groups = self._group_by_applications()

        for application, products in list(application_groups.items())[:10]:  # 只显示前10个应用场景
            guide_content.append(f"### {application}\n")
            guide_content.append("**推荐产品**:\n")

            for product in products[:3]:  # 每个应用场景推荐3个产品
                name = product.get('name', '未知产品')
                specs = product.get('technical_specs', {})
                key_specs = self._extract_key_specs(specs, max_count=2)

                guide_content.append(f"- **{name}**\n")
                if key_specs:
                    for key, value in key_specs.items():
                        guide_content.append(f"  - {key}: {value}\n")
                guide_content.append("")

            guide_content.append("---\n")

        guide_file = output_dir / "客户需求快速匹配指南.md"
        with open(guide_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(guide_content))

        logger.info(f"客户需求匹配指南已保存: {guide_file}")

    async def generate_sales_script_library(self, output_dir: Path):
        """生成销售话术库"""
        logger.info("生成销售话术库...")

        script_content = []
        script_content.append("# 专业销售话术库\n")
        script_content.append(f"生成时间: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}\n")
        script_content.append("---\n")

        script_content.append("## 开场白话术\n")
        script_content.append("### 专业开场白\n")
        script_content.append('"您好！我是赵中AI的技术销售顾问。了解到您在寻找传感器解决方案，我们有超过{}款专业产品，覆盖{}个主要应用领域，相信能为您提供最适合的解决方案。"\n'.format(
            len(self.products_data), len(self._group_by_applications())
        ))
        script_content.append("**使用场景**: 初次接触客户，建立专业形象\n")

        script_content.append("### 需求探索开场\n")
        script_content.append('"请问您目前的项目主要需要检测哪些参数？我可以根据您的具体需求推荐最合适的产品。"\n')
        script_content.append("**使用场景**: 客户有明确项目需求时\n")

        script_content.append("## 产品介绍话术\n")

        # 基于实际产品生成介绍话术
        featured_products = self.products_data[:5]  # 选择前5个产品作为重点推荐

        for i, product in enumerate(featured_products, 1):
            name = product.get('name', '产品')
            features = product.get('features', [])
            specs = product.get('technical_specs', {})
            applications = product.get('applications', [])

            script_content.append(f"### {name} 介绍话术\n")

            # 基于产品特点生成话术
            if features:
                main_features = features[:3]
                script_content.append(f'"这款{name}的主要优势是{", ".join(main_features)}，特别适合{", ".join(applications[:2]) if applications else "工业应用"}。"\n')

            # 基于技术规格生成话术
            key_specs = self._extract_key_specs(specs, max_count=2)
            if key_specs:
                spec_desc = []
                for key, value in key_specs.items():
                    spec_desc.append(f"{key}{value}")
                script_content.append(f'"在技术参数方面，{", ".join(spec_desc)}，完全满足您的应用要求。"\n')

            script_content.append("---\n")

        script_content.append("## 异议处理话术\n")

        script_content.append("### 价格异议处理\n")
        script_content.append("**客户**: \"你们的价格比其他厂家贵\"\n")
        script_content.append("**回应**: \"我理解您对成本的考虑。我们的产品虽然初期投资稍高，但从长期使用成本来看是非常划算的：\n")
        script_content.append("1. 高精度减少误差损失\n")
        script_content.append("2. 高稳定性降低维护成本\n")
        script_content.append("3. 长寿命减少更换频率\n")
        script_content.append("综合计算，实际使用成本更低。\"\n")

        script_content.append("### 技术质疑处理\n")
        script_content.append("**客户**: \"你们的技术真的可靠吗？\"\n")
        script_content.append("**回应**: \"我完全理解您的担心。我们的产品都经过严格的质量认证，并且在{}个不同应用场景中得到验证。我可以提供详细的技术资料和应用案例供您参考。\"\n".format(
            len(self._group_by_applications())
        ))

        script_content.append("### 决策延迟处理\n")
        script_content.append("**客户**: \"我需要再考虑考虑\"\n")
        script_content.append("**回应**: \"当然，这样重要的决策确实需要慎重考虑。请问您主要担心哪些方面？是技术适配性、价格，还是售后服务？我可以针对性地为您提供更多信息。\"\n")

        script_content.append("## 成交促进话术\n")

        script_content.append("### 紧迫性营造\n")
        script_content.append('"根据我们的经验，越早实施自动化改造，投资回报越快。而且我们目前有技术支持优惠政策，可以为您提供免费的现场技术指导。"\n')

        script_content.append("### 风险消除\n")
        script_content.append('"为了让您更放心，我们可以先提供样品测试，确认完全满足您的要求后再正式采购。这样可以完全消除您的技术风险。"\n')

        script_file = output_dir / "专业销售话术库.md"
        with open(script_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(script_content))

        logger.info(f"销售话术库已保存: {script_file}")

    def save_products_json(self, output_dir: Path):
        """保存产品数据为JSON格式"""
        json_file = output_dir / "products_data.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(self.products_data, f, ensure_ascii=False, indent=2)
        logger.info(f"产品数据已保存: {json_file}")

    def get_statistics(self) -> Dict[str, Any]:
        """获取产品数据统计信息"""
        stats = {
            'total_products': len(self.products_data),
            'categories': len(self.categories),
            'total_subcategories': sum(len(subcats) for subcats in self.categories.values()),
            'products_with_pdf': len([p for p in self.products_data if p.get('pdf_url')]),
            'products_with_specs': len([p for p in self.products_data if p.get('technical_specs')]),
            'products_with_applications': len([p for p in self.products_data if p.get('applications')]),
            'products_with_features': len([p for p in self.products_data if p.get('features')]),
            'total_applications': len(self._group_by_applications()),
            'avg_specs_per_product': sum(len(p.get('technical_specs', {})) for p in self.products_data) / len(self.products_data) if self.products_data else 0,
            'avg_features_per_product': sum(len(p.get('features', [])) for p in self.products_data) / len(self.products_data) if self.products_data else 0
        }
        return stats
