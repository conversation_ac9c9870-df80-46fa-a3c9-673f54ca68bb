#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
赵中AI网站爬虫模块
使用Playwright自动化爬取产品信息
"""

import asyncio
import logging
import re
from typing import List, Dict, Any, Optional
from urllib.parse import urljoin, urlparse
from playwright.async_api import async_playwright, Page, Browser
import json

logger = logging.getLogger(__name__)

class ZhaoZhongAIScraper:
    """赵中AI网站爬虫类"""
    
    def __init__(self):
        self.base_url = "https://www.zhaozhongai.com"
        self.browser: Optional[Browser] = None
        self.page: Optional[Page] = None
        self.products_data = []
        
    async def initialize(self):
        """初始化浏览器"""
        logger.info("初始化浏览器...")
        self.playwright = await async_playwright().start()
        self.browser = await self.playwright.chromium.launch(
            headless=True,
            args=['--no-sandbox', '--disable-dev-shm-usage']
        )
        self.page = await self.browser.new_page()
        
        # 设置用户代理
        await self.page.set_extra_http_headers({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        })
        
    async def close(self):
        """关闭浏览器"""
        if self.browser:
            await self.browser.close()
        if hasattr(self, 'playwright'):
            await self.playwright.stop()
            
    async def scrape_all_products(self) -> List[Dict[str, Any]]:
        """爬取所有产品信息"""
        try:
            # 访问主页
            logger.info(f"访问网站: {self.base_url}")
            await self.page.goto(self.base_url, wait_until='networkidle')
            
            # 获取产品分类链接
            category_links = await self.get_product_categories()
            logger.info(f"发现 {len(category_links)} 个产品分类")
            
            all_products = []
            
            for category_name, category_url in category_links.items():
                logger.info(f"爬取分类: {category_name}")
                category_products = await self.scrape_category_products(category_name, category_url)
                all_products.extend(category_products)
                
                # 添加延迟避免被反爬
                await asyncio.sleep(2)
                
            logger.info(f"总共爬取到 {len(all_products)} 个产品")
            return all_products
            
        except Exception as e:
            logger.error(f"爬取过程出错: {str(e)}")
            raise
            
    async def get_product_categories(self) -> Dict[str, str]:
        """获取产品分类链接"""
        categories = {}
        
        try:
            # 查找导航菜单中的产品分类
            nav_selectors = [
                'nav a[href*="product"]',
                '.nav a[href*="product"]',
                '.menu a[href*="product"]',
                'a[href*="category"]',
                'a[href*="产品"]'
            ]
            
            for selector in nav_selectors:
                try:
                    links = await self.page.query_selector_all(selector)
                    for link in links:
                        href = await link.get_attribute('href')
                        text = await link.inner_text()
                        
                        if href and text and text.strip():
                            full_url = urljoin(self.base_url, href)
                            categories[text.strip()] = full_url
                            
                    if categories:
                        break
                        
                except Exception as e:
                    logger.debug(f"选择器 {selector} 未找到元素: {e}")
                    continue
                    
            # 如果没有找到分类，尝试查找所有产品链接
            if not categories:
                logger.info("未找到明确的产品分类，尝试查找所有产品链接")
                product_links = await self.find_all_product_links()
                if product_links:
                    categories["所有产品"] = self.base_url
                    
        except Exception as e:
            logger.error(f"获取产品分类失败: {str(e)}")
            # 默认返回主页作为分类
            categories["默认分类"] = self.base_url
            
        return categories
        
    async def find_all_product_links(self) -> List[str]:
        """查找页面中所有可能的产品链接"""
        product_links = []
        
        # 常见的产品链接模式
        link_patterns = [
            r'.*product.*',
            r'.*item.*',
            r'.*detail.*',
            r'.*sensor.*',
            r'.*设备.*',
            r'.*产品.*'
        ]
        
        try:
            all_links = await self.page.query_selector_all('a[href]')
            
            for link in all_links:
                href = await link.get_attribute('href')
                text = await link.inner_text()
                
                if href:
                    full_url = urljoin(self.base_url, href)
                    
                    # 检查链接是否匹配产品模式
                    for pattern in link_patterns:
                        if re.search(pattern, href, re.IGNORECASE) or re.search(pattern, text, re.IGNORECASE):
                            product_links.append(full_url)
                            break
                            
        except Exception as e:
            logger.error(f"查找产品链接失败: {str(e)}")
            
        return list(set(product_links))  # 去重
        
    async def scrape_category_products(self, category_name: str, category_url: str) -> List[Dict[str, Any]]:
        """爬取特定分类的产品"""
        products = []
        
        try:
            await self.page.goto(category_url, wait_until='networkidle')
            await asyncio.sleep(1)
            
            # 查找产品列表
            product_links = await self.find_product_links_in_page()
            
            logger.info(f"在分类 '{category_name}' 中找到 {len(product_links)} 个产品链接")
            
            for i, product_url in enumerate(product_links[:20]):  # 限制每个分类最多20个产品
                try:
                    logger.info(f"爬取产品 {i+1}/{len(product_links[:20])}: {product_url}")
                    product_data = await self.scrape_product_detail(product_url, category_name)
                    
                    if product_data:
                        products.append(product_data)
                        
                    # 添加延迟
                    await asyncio.sleep(1)
                    
                except Exception as e:
                    logger.error(f"爬取产品详情失败 {product_url}: {str(e)}")
                    continue
                    
        except Exception as e:
            logger.error(f"爬取分类产品失败 {category_name}: {str(e)}")
            
        return products
        
    async def find_product_links_in_page(self) -> List[str]:
        """在当前页面查找产品链接"""
        product_links = []
        
        # 多种选择器策略
        selectors = [
            'a[href*="product"]',
            'a[href*="item"]',
            'a[href*="detail"]',
            '.product-item a',
            '.product-list a',
            '.item a',
            'a[title]',
            'a img'  # 包含图片的链接通常是产品链接
        ]
        
        for selector in selectors:
            try:
                links = await self.page.query_selector_all(selector)
                
                for link in links:
                    href = await link.get_attribute('href')
                    if href:
                        full_url = urljoin(self.base_url, href)
                        
                        # 过滤掉明显不是产品的链接
                        if not any(exclude in href.lower() for exclude in ['javascript:', 'mailto:', '#', 'tel:']):
                            product_links.append(full_url)
                            
            except Exception as e:
                logger.debug(f"选择器 {selector} 查找失败: {e}")
                continue
                
        # 去重并返回
        return list(set(product_links))
        
    async def scrape_product_detail(self, product_url: str, category: str) -> Optional[Dict[str, Any]]:
        """爬取单个产品的详细信息"""
        try:
            await self.page.goto(product_url, wait_until='networkidle')
            await asyncio.sleep(1)
            
            # 提取产品基本信息
            product_data = {
                'url': product_url,
                'category': category,
                'name': await self.extract_product_name(),
                'description': await self.extract_product_description(),
                'price': await self.extract_product_price(),
                'images': await self.extract_product_images(),
                'technical_specs': await self.extract_technical_specs(),
                'applications': await self.extract_applications(),
                'features': await self.extract_features(),
                'scraped_at': asyncio.get_event_loop().time()
            }
            
            # 验证数据完整性
            if not product_data['name']:
                logger.warning(f"产品名称为空，跳过: {product_url}")
                return None
                
            return product_data
            
        except Exception as e:
            logger.error(f"爬取产品详情失败: {str(e)}")
            return None
            
    async def extract_product_name(self) -> str:
        """提取产品名称"""
        selectors = [
            'h1',
            '.product-title',
            '.product-name',
            '.title',
            'title',
            '.page-title'
        ]
        
        for selector in selectors:
            try:
                element = await self.page.query_selector(selector)
                if element:
                    text = await element.inner_text()
                    if text and text.strip():
                        return text.strip()
            except:
                continue
                
        return ""
        
    async def extract_product_description(self) -> str:
        """提取产品描述"""
        selectors = [
            '.product-description',
            '.description',
            '.product-intro',
            '.intro',
            '.content',
            'meta[name="description"]'
        ]
        
        for selector in selectors:
            try:
                if selector.startswith('meta'):
                    element = await self.page.query_selector(selector)
                    if element:
                        content = await element.get_attribute('content')
                        if content:
                            return content.strip()
                else:
                    element = await self.page.query_selector(selector)
                    if element:
                        text = await element.inner_text()
                        if text and len(text.strip()) > 10:
                            return text.strip()
            except:
                continue
                
        return ""
        
    async def extract_product_price(self) -> str:
        """提取产品价格"""
        selectors = [
            '.price',
            '.product-price',
            '.cost',
            '[class*="price"]',
            '[id*="price"]'
        ]
        
        for selector in selectors:
            try:
                element = await self.page.query_selector(selector)
                if element:
                    text = await element.inner_text()
                    # 查找价格模式
                    price_pattern = r'[¥$€£]\s*[\d,]+\.?\d*|[\d,]+\.?\d*\s*[¥$€£元]'
                    match = re.search(price_pattern, text)
                    if match:
                        return match.group().strip()
            except:
                continue
                
        return ""
        
    async def extract_product_images(self) -> List[str]:
        """提取产品图片"""
        images = []
        
        selectors = [
            '.product-image img',
            '.product-gallery img',
            '.gallery img',
            'img[alt*="产品"]',
            'img[src*="product"]'
        ]
        
        for selector in selectors:
            try:
                img_elements = await self.page.query_selector_all(selector)
                for img in img_elements:
                    src = await img.get_attribute('src')
                    if src:
                        full_url = urljoin(self.base_url, src)
                        images.append(full_url)
            except:
                continue
                
        return list(set(images))  # 去重
        
    async def extract_technical_specs(self) -> Dict[str, str]:
        """提取技术规格参数"""
        specs = {}
        
        try:
            # 查找参数表格
            tables = await self.page.query_selector_all('table')
            
            for table in tables:
                rows = await table.query_selector_all('tr')
                
                for row in rows:
                    cells = await row.query_selector_all('td, th')
                    
                    if len(cells) >= 2:
                        key_cell = cells[0]
                        value_cell = cells[1]
                        
                        key = await key_cell.inner_text()
                        value = await value_cell.inner_text()
                        
                        if key and value:
                            specs[key.strip()] = value.strip()
                            
            # 如果没有找到表格，尝试查找其他格式的参数
            if not specs:
                specs = await self.extract_specs_from_text()
                
        except Exception as e:
            logger.debug(f"提取技术规格失败: {e}")
            
        return specs
        
    async def extract_specs_from_text(self) -> Dict[str, str]:
        """从文本中提取规格参数"""
        specs = {}
        
        try:
            # 获取页面所有文本
            page_text = await self.page.inner_text('body')
            
            # 常见的参数模式
            patterns = [
                r'([^:\n]+)[:：]\s*([^\n]+)',
                r'([^=\n]+)[=]\s*([^\n]+)',
                r'([^-\n]+)[-]\s*([^\n]+)'
            ]
            
            for pattern in patterns:
                matches = re.findall(pattern, page_text)
                
                for key, value in matches:
                    key = key.strip()
                    value = value.strip()
                    
                    # 过滤掉明显不是参数的内容
                    if (len(key) < 50 and len(value) < 200 and 
                        not any(exclude in key.lower() for exclude in ['http', 'www', 'com', '版权', 'copyright'])):
                        specs[key] = value
                        
        except Exception as e:
            logger.debug(f"从文本提取规格失败: {e}")
            
        return specs
        
    async def extract_applications(self) -> List[str]:
        """提取应用场景"""
        applications = []
        
        try:
            # 查找应用相关的文本
            selectors = [
                '.applications',
                '.application',
                '.use-case',
                '.scenario',
                '[class*="应用"]',
                '[class*="场景"]'
            ]
            
            for selector in selectors:
                try:
                    elements = await self.page.query_selector_all(selector)
                    for element in elements:
                        text = await element.inner_text()
                        if text:
                            # 分割应用场景
                            apps = re.split(r'[,，;；\n]', text)
                            for app in apps:
                                app = app.strip()
                                if app and len(app) > 2:
                                    applications.append(app)
                except:
                    continue
                    
        except Exception as e:
            logger.debug(f"提取应用场景失败: {e}")
            
        return list(set(applications))  # 去重
        
    async def extract_features(self) -> List[str]:
        """提取产品特点"""
        features = []
        
        try:
            # 查找特点相关的文本
            selectors = [
                '.features',
                '.feature',
                '.advantages',
                '.advantage',
                '[class*="特点"]',
                '[class*="优势"]',
                'ul li',
                '.list li'
            ]
            
            for selector in selectors:
                try:
                    elements = await self.page.query_selector_all(selector)
                    for element in elements:
                        text = await element.inner_text()
                        if text and len(text.strip()) > 5 and len(text.strip()) < 200:
                            features.append(text.strip())
                except:
                    continue
                    
        except Exception as e:
            logger.debug(f"提取产品特点失败: {e}")
            
        return list(set(features))  # 去重