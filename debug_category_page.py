#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
<AUTHOR> 
@license          http://www.178188.xyz
@lastmodify       2025年8月1日
模块说明: 调试分类页面结构
"""

import asyncio
import logging
from playwright.async_api import async_playwright

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def debug_category_page():
    """调试分类页面结构"""
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False)  # 显示浏览器
        page = await browser.new_page()
        
        try:
            # 测试CPU单元分类页面
            url = "https://www.zhaozhongai.com/productlist1/"
            logger.info(f"访问分类页面: {url}")
            await page.goto(url, wait_until='networkidle')
            
            # 等待页面完全加载
            await asyncio.sleep(5)
            
            # 获取页面标题
            title = await page.title()
            logger.info(f"页面标题: {title}")
            
            # 查找所有链接
            links = await page.query_selector_all('a')
            logger.info(f"找到 {len(links)} 个链接")
            
            # 查找PDF链接
            pdf_links = []
            for link in links:
                href = await link.get_attribute('href')
                text = await link.inner_text()
                if href and '.pdf' in href:
                    pdf_links.append({
                        'text': text.strip(),
                        'href': href
                    })
            
            logger.info(f"找到 {len(pdf_links)} 个PDF链接:")
            for i, link in enumerate(pdf_links):
                logger.info(f"  {i+1}. {link['text']} -> {link['href']}")
            
            # 查找可能的产品容器
            logger.info("\n查找产品容器...")
            
            # 尝试不同的选择器
            selectors_to_try = [
                '.product-list',
                '.product-item',
                '.item',
                '.card',
                '.box',
                '[class*="product"]',
                '[class*="item"]',
                '[id*="product"]',
                '.layui-row',
                '.content',
                '.main'
            ]
            
            for selector in selectors_to_try:
                try:
                    elements = await page.query_selector_all(selector)
                    if elements:
                        logger.info(f"选择器 '{selector}' 找到 {len(elements)} 个元素")
                        
                        # 检查前几个元素的内容
                        for i, element in enumerate(elements[:3]):
                            text = await element.inner_text()
                            if text and len(text.strip()) > 10:
                                logger.info(f"  元素 {i+1} 内容预览: {text[:100]}...")
                except Exception as e:
                    logger.debug(f"选择器 {selector} 失败: {e}")
            
            # 检查是否有JavaScript动态加载
            logger.info("\n检查JavaScript动态加载...")
            
            # 等待可能的AJAX请求
            await asyncio.sleep(3)
            
            # 尝试滚动页面触发懒加载
            await page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
            await asyncio.sleep(2)
            
            # 再次查找PDF链接
            new_pdf_links = await page.query_selector_all('a[href*=".pdf"]')
            logger.info(f"滚动后找到 {len(new_pdf_links)} 个PDF链接")
            
            # 查找可能的加载按钮或触发器
            load_buttons = await page.query_selector_all('button, .btn, .load-more, .more, [onclick]')
            logger.info(f"找到 {len(load_buttons)} 个可能的加载按钮")
            
            for i, button in enumerate(load_buttons[:5]):
                text = await button.inner_text()
                onclick = await button.get_attribute('onclick')
                if text or onclick:
                    logger.info(f"  按钮 {i+1}: '{text}' onclick='{onclick}'")
            
            # 尝试点击可能的加载按钮
            for button in load_buttons:
                try:
                    text = await button.inner_text()
                    if text and any(keyword in text.lower() for keyword in ['加载', 'load', '更多', 'more', '显示', 'show']):
                        logger.info(f"尝试点击按钮: {text}")
                        await button.click()
                        await asyncio.sleep(3)
                        
                        # 检查是否有新的PDF链接出现
                        updated_pdf_links = await page.query_selector_all('a[href*=".pdf"]')
                        if len(updated_pdf_links) > len(new_pdf_links):
                            logger.info(f"点击后新增了 {len(updated_pdf_links) - len(new_pdf_links)} 个PDF链接")
                        break
                except Exception as e:
                    logger.debug(f"点击按钮失败: {e}")
            
            # 检查页面源码中是否有隐藏的产品信息
            logger.info("\n检查页面源码...")
            html_content = await page.content()
            
            # 搜索可能的产品信息模式
            import re
            
            # 查找可能的产品名称模式
            product_patterns = [
                r'NX\d+[-\w]*',  # NX系列产品
                r'E\d+[A-Z]+[-\w]*',  # E系列传感器
                r'CPU[-\w]*',
                r'PLC[-\w]*'
            ]
            
            found_products = set()
            for pattern in product_patterns:
                matches = re.findall(pattern, html_content, re.IGNORECASE)
                found_products.update(matches)
            
            if found_products:
                logger.info(f"在页面源码中找到可能的产品型号: {list(found_products)[:10]}")
            
            # 查找可能的PDF URL模式
            pdf_urls = re.findall(r'https?://[^\s"\']*\.pdf', html_content)
            if pdf_urls:
                logger.info(f"在页面源码中找到PDF URL: {pdf_urls[:5]}")
            
            # 保存页面截图和HTML
            await page.screenshot(path="category_page_debug.png")
            logger.info("页面截图已保存: category_page_debug.png")
            
            with open("category_page_debug.html", "w", encoding="utf-8") as f:
                f.write(html_content)
            logger.info("页面HTML已保存: category_page_debug.html")
            
            # 尝试其他分类页面
            other_urls = [
                "https://www.zhaozhongai.com/productlist2/",  # 总线耦合器
                "https://www.zhaozhongai.com/mokuai/",  # IO模块
            ]
            
            for test_url in other_urls:
                logger.info(f"\n测试其他分类页面: {test_url}")
                await page.goto(test_url, wait_until='networkidle')
                await asyncio.sleep(3)
                
                test_pdf_links = await page.query_selector_all('a[href*=".pdf"]')
                logger.info(f"在 {test_url} 找到 {len(test_pdf_links)} 个PDF链接")
                
                if test_pdf_links:
                    for i, link in enumerate(test_pdf_links[:3]):
                        href = await link.get_attribute('href')
                        text = await link.inner_text()
                        logger.info(f"  PDF {i+1}: {text.strip()} -> {href}")
                    break  # 如果找到PDF链接就停止测试
            
        except Exception as e:
            logger.error(f"调试过程出错: {e}")
            
        finally:
            await browser.close()

if __name__ == "__main__":
    asyncio.run(debug_category_page())
