#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
赵中AI产品爬虫和销售工具生成器
为新入职销售人员提供完整的产品知识库和销售支持工具
"""

import asyncio
import json
import logging
from pathlib import Path
from datetime import datetime

from src.scraper import ZhaoZhongAIScraper
from src.data_processor import DataProcessor
from src.document_generator import DocumentGenerator

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('scraper.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

async def main():
    """主程序入口"""
    logger.info("开始执行赵中AI产品数据爬取和销售工具生成")
    
    # 创建输出目录
    output_dir = Path("output")
    output_dir.mkdir(exist_ok=True)
    
    try:
        # 1. 数据爬取阶段
        logger.info("步骤1: 开始爬取产品数据...")
        scraper = ZhaoZhongAIScraper()
        await scraper.initialize()
        
        # 爬取所有产品数据
        products_data = await scraper.scrape_all_products()
        
        # 保存原始数据
        raw_data_file = output_dir / f"raw_products_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(raw_data_file, 'w', encoding='utf-8') as f:
            json.dump(products_data, f, ensure_ascii=False, indent=2)
        
        logger.info(f"爬取完成，共获取 {len(products_data)} 个产品信息")
        
        await scraper.close()
        
        # 2. 数据处理阶段
        logger.info("步骤2: 开始处理和分析数据...")
        processor = DataProcessor()
        
        # 清洗和结构化数据
        clean_data = processor.clean_and_structure_data(products_data)
        
        # 建立产品分类体系
        product_categories = processor.categorize_products(clean_data)
        
        # 提取技术参数
        technical_specs = processor.extract_technical_specifications(clean_data)
        
        # 3. 销售工具生成阶段
        logger.info("步骤3: 开始生成销售支持文档...")
        generator = DocumentGenerator(clean_data, product_categories, technical_specs)
        
        # 生成所有销售工具文档
        await generator.generate_all_documents(output_dir)
        
        logger.info("所有销售工具生成完成！")
        logger.info(f"输出文件保存在: {output_dir.absolute()}")
        
        # 显示生成的文件列表
        print("\n生成的销售工具文件:")
        for file in sorted(output_dir.glob("*")):
            print(f"  - {file.name}")
            
    except Exception as e:
        logger.error(f"程序执行出错: {str(e)}")
        raise

if __name__ == "__main__":
    asyncio.run(main())