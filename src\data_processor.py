#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据处理模块
清洗、结构化和分析爬取的产品数据
"""

import re
import logging
from typing import List, Dict, Any, Optional, Tuple
from collections import defaultdict
import json

logger = logging.getLogger(__name__)

class DataProcessor:
    """数据处理器类"""
    
    def __init__(self):
        self.clean_data = []
        self.categories = {}
        self.technical_specs = {}
        
        # 技术参数标准化映射
        self.spec_mapping = {
            # 精度相关
            '精度': 'accuracy',
            '准确度': 'accuracy',
            'accuracy': 'accuracy',
            '误差': 'error',
            
            # 量程相关
            '量程': 'range',
            '测量范围': 'range',
            '检测范围': 'detection_range',
            '工作范围': 'working_range',
            'range': 'range',
            
            # 分辨率
            '分辨率': 'resolution',
            'resolution': 'resolution',
            
            # 响应时间
            '响应时间': 'response_time',
            '反应时间': 'response_time',
            'response time': 'response_time',
            
            # 工作温度
            '工作温度': 'working_temperature',
            '使用温度': 'working_temperature',
            '操作温度': 'working_temperature',
            'working temperature': 'working_temperature',
            
            # 存储温度
            '存储温度': 'storage_temperature',
            '储存温度': 'storage_temperature',
            'storage temperature': 'storage_temperature',
            
            # 湿度
            '工作湿度': 'working_humidity',
            '相对湿度': 'relative_humidity',
            'humidity': 'working_humidity',
            
            # 电源
            '工作电压': 'working_voltage',
            '供电电压': 'power_voltage',
            '电源电压': 'power_voltage',
            'voltage': 'working_voltage',
            
            # 功耗
            '功耗': 'power_consumption',
            '耗电': 'power_consumption',
            'power consumption': 'power_consumption',
            
            # 输出信号
            '输出信号': 'output_signal',
            '输出': 'output_signal',
            'output': 'output_signal',
            
            # 接口
            '接口': 'interface',
            '通信接口': 'interface',
            'interface': 'interface',
            
            # 尺寸
            '尺寸': 'dimensions',
            '外形尺寸': 'dimensions',
            '大小': 'dimensions',
            'size': 'dimensions',
            'dimensions': 'dimensions',
            
            # 重量
            '重量': 'weight',
            'weight': 'weight',
            
            # 防护等级
            '防护等级': 'protection_level',
            'IP等级': 'protection_level',
            'protection level': 'protection_level',
            
            # 认证
            '认证': 'certification',
            '证书': 'certification',
            'certification': 'certification',
            
            # 寿命
            '使用寿命': 'lifespan',
            '寿命': 'lifespan',
            'lifespan': 'lifespan',
            
            # 稳定性
            '稳定性': 'stability',
            'stability': 'stability',
            
            # 重复性
            '重复性': 'repeatability',
            'repeatability': 'repeatability'
        }
        
        # 应用场景分类
        self.application_categories = {
            '工业自动化': ['自动化', '生产线', '制造', '工厂', '机械', '设备监控'],
            '环境监测': ['环境', '气象', '空气质量', '污染', '温湿度', '气体检测'],
            '安防监控': ['安防', '监控', '报警', '入侵检测', '周界', '安全'],
            '智能家居': ['家居', '智能家庭', '家用', '室内', '生活'],
            '交通运输': ['交通', '车辆', '道路', '停车', '物流', '运输'],
            '医疗健康': ['医疗', '健康', '医院', '诊断', '监护'],
            '农业': ['农业', '种植', '养殖', '温室', '大棚', '农田'],
            '能源': ['能源', '电力', '太阳能', '风能', '节能'],
            '建筑': ['建筑', '楼宇', '暖通', 'HVAC', '智能建筑'],
            '科研教育': ['科研', '实验', '教育', '学校', '研究']
        }
        
    def clean_and_structure_data(self, raw_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """清洗和结构化原始数据"""
        logger.info("开始清洗和结构化数据...")
        
        clean_data = []
        
        for i, product in enumerate(raw_data):
            try:
                cleaned_product = self._clean_single_product(product, i)
                if cleaned_product:
                    clean_data.append(cleaned_product)
                    
            except Exception as e:
                logger.error(f"清洗产品数据失败 {i}: {str(e)}")
                continue
                
        logger.info(f"数据清洗完成，有效产品数量: {len(clean_data)}")
        self.clean_data = clean_data
        return clean_data
        
    def _clean_single_product(self, product: Dict[str, Any], index: int) -> Optional[Dict[str, Any]]:
        """清洗单个产品数据"""
        
        # 验证必要字段
        if not product.get('name') or not product.get('name').strip():
            logger.warning(f"产品 {index} 缺少名称，跳过")
            return None
            
        cleaned = {
            'id': f"product_{index:04d}",
            'name': self._clean_text(product.get('name', '')),
            'category': self._clean_text(product.get('category', '未分类')),
            'description': self._clean_text(product.get('description', '')),
            'price': self._clean_price(product.get('price', '')),
            'url': product.get('url', ''),
            'images': self._clean_images(product.get('images', [])),
            'technical_specs': self._standardize_technical_specs(product.get('technical_specs', {})),
            'applications': self._clean_applications(product.get('applications', [])),
            'features': self._clean_features(product.get('features', [])),
            'target_industries': self._extract_target_industries(product),
            'key_selling_points': self._extract_selling_points(product),
            'scraped_at': product.get('scraped_at', 0)
        }
        
        return cleaned
        
    def _clean_text(self, text: str) -> str:
        """清洗文本内容"""
        if not text:
            return ""
            
        # 移除多余空白字符
        text = re.sub(r'\s+', ' ', text.strip())
        
        # 移除特殊字符
        text = re.sub(r'[^\w\s\u4e00-\u9fff.,()（）-]', '', text)
        
        return text
        
    def _clean_price(self, price: str) -> Dict[str, Any]:
        """清洗价格信息"""
        if not price:
            return {'raw': '', 'currency': '', 'amount': 0, 'formatted': ''}
            
        # 提取货币符号和数字
        currency_pattern = r'[¥$€£元]'
        number_pattern = r'[\d,]+\.?\d*'
        
        currency_match = re.search(currency_pattern, price)
        number_match = re.search(number_pattern, price)
        
        currency = currency_match.group() if currency_match else ''
        amount_str = number_match.group() if number_match else '0'
        
        # 转换为数字
        try:
            amount = float(amount_str.replace(',', ''))
        except:
            amount = 0
            
        return {
            'raw': price,
            'currency': currency,
            'amount': amount,
            'formatted': f"{currency}{amount:,.2f}" if currency and amount > 0 else price
        }
        
    def _clean_images(self, images: List[str]) -> List[str]:
        """清洗图片链接"""
        clean_images = []
        
        for img in images:
            if img and isinstance(img, str):
                # 验证是否为有效URL
                if img.startswith(('http://', 'https://', '//')):
                    clean_images.append(img)
                    
        return clean_images
        
    def _standardize_technical_specs(self, specs: Dict[str, str]) -> Dict[str, Any]:
        """标准化技术规格参数"""
        standardized = {}
        
        for key, value in specs.items():
            if not key or not value:
                continue
                
            # 清洗键名
            clean_key = self._clean_text(key.lower())
            
            # 映射到标准参数名
            standard_key = self.spec_mapping.get(clean_key, clean_key)
            
            # 清洗和解析值
            parsed_value = self._parse_spec_value(value, standard_key)
            
            standardized[standard_key] = parsed_value
            
        return standardized
        
    def _parse_spec_value(self, value: str, spec_type: str) -> Dict[str, Any]:
        """解析规格参数值"""
        if not value:
            return {'raw': '', 'value': '', 'unit': '', 'numeric': None}
            
        clean_value = self._clean_text(value)
        
        # 提取数值和单位
        numeric_pattern = r'([-+]?\d*\.?\d+)'
        unit_pattern = r'([a-zA-Z%°℃℉]+|[^\d\s.,+-]+)'
        
        numeric_matches = re.findall(numeric_pattern, clean_value)
        unit_matches = re.findall(unit_pattern, clean_value)
        
        # 获取第一个数值
        numeric_value = None
        if numeric_matches:
            try:
                numeric_value = float(numeric_matches[0])
            except:
                pass
                
        # 获取单位
        unit = ''
        if unit_matches:
            unit = unit_matches[-1].strip()  # 取最后一个作为单位
            
        return {
            'raw': value,
            'value': clean_value,
            'unit': unit,
            'numeric': numeric_value
        }
        
    def _clean_applications(self, applications: List[str]) -> List[str]:
        """清洗应用场景"""
        clean_apps = []
        
        for app in applications:
            if app and isinstance(app, str):
                clean_app = self._clean_text(app)
                if len(clean_app) > 2 and len(clean_app) < 100:
                    clean_apps.append(clean_app)
                    
        return list(set(clean_apps))  # 去重
        
    def _clean_features(self, features: List[str]) -> List[str]:
        """清洗产品特点"""
        clean_features = []
        
        for feature in features:
            if feature and isinstance(feature, str):
                clean_feature = self._clean_text(feature)
                if len(clean_feature) > 5 and len(clean_feature) < 200:
                    clean_features.append(clean_feature)
                    
        return list(set(clean_features))  # 去重
        
    def _extract_target_industries(self, product: Dict[str, Any]) -> List[str]:
        """提取目标行业"""
        industries = []
        
        # 从应用场景推断行业
        applications = product.get('applications', [])
        description = product.get('description', '')
        features = product.get('features', [])
        
        all_text = ' '.join(applications + [description] + features).lower()
        
        for industry, keywords in self.application_categories.items():
            for keyword in keywords:
                if keyword in all_text:
                    industries.append(industry)
                    break
                    
        return list(set(industries))
        
    def _extract_selling_points(self, product: Dict[str, Any]) -> List[str]:
        """提取关键卖点"""
        selling_points = []
        
        # 从特点中提取卖点
        features = product.get('features', [])
        for feature in features:
            if any(keyword in feature.lower() for keyword in ['高精度', '高稳定', '低功耗', '防水', '耐用', '智能', '自动']):
                selling_points.append(feature)
                
        # 从技术规格中提取优势
        specs = product.get('technical_specs', {})
        for key, value in specs.items():
            if '精度' in key and value:
                selling_points.append(f"高精度: {value}")
            elif '温度' in key and value:
                selling_points.append(f"宽温度范围: {value}")
                
        return selling_points[:5]  # 限制最多5个卖点
        
    def categorize_products(self, products: List[Dict[str, Any]]) -> Dict[str, Any]:
        """建立产品分类体系"""
        logger.info("建立产品分类体系...")
        
        categories = defaultdict(list)
        
        for product in products:
            category = product.get('category', '未分类')
            categories[category].append(product)
            
        # 构建分类树
        category_tree = {}
        for category, products_list in categories.items():
            category_tree[category] = {
                'name': category,
                'count': len(products_list),
                'products': products_list,
                'subcategories': self._create_subcategories(products_list)
            }
            
        logger.info(f"创建了 {len(category_tree)} 个主要分类")
        self.categories = category_tree
        return category_tree
        
    def _create_subcategories(self, products: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
        """创建子分类"""
        subcategories = defaultdict(list)
        
        for product in products:
            # 根据产品名称或特点创建子分类
            name = product.get('name', '').lower()
            
            if '传感器' in name:
                if any(keyword in name for keyword in ['温度', '温湿度']):
                    subcategories['温度传感器'].append(product)
                elif any(keyword in name for keyword in ['压力', '压强']):
                    subcategories['压力传感器'].append(product)
                elif any(keyword in name for keyword in ['距离', '位移', '接近']):
                    subcategories['距离传感器'].append(product)
                elif any(keyword in name for keyword in ['光', '光电']):
                    subcategories['光电传感器'].append(product)
                else:
                    subcategories['其他传感器'].append(product)
            else:
                subcategories['其他设备'].append(product)
                
        return dict(subcategories)
        
    def extract_technical_specifications(self, products: List[Dict[str, Any]]) -> Dict[str, Any]:
        """提取和分析技术规格"""
        logger.info("提取技术规格参数...")
        
        all_specs = {}
        spec_stats = defaultdict(list)
        
        for product in products:
            product_id = product.get('id')
            specs = product.get('technical_specs', {})
            
            all_specs[product_id] = specs
            
            # 统计各参数的分布
            for spec_name, spec_data in specs.items():
                if isinstance(spec_data, dict) and spec_data.get('numeric') is not None:
                    spec_stats[spec_name].append(spec_data['numeric'])
                    
        # 计算参数统计信息
        spec_analysis = {}
        for spec_name, values in spec_stats.items():
            if values:
                spec_analysis[spec_name] = {
                    'count': len(values),
                    'min': min(values),
                    'max': max(values),
                    'avg': sum(values) / len(values),
                    'values': values
                }
                
        self.technical_specs = {
            'all_specs': all_specs,
            'analysis': spec_analysis
        }
        
        logger.info(f"分析了 {len(spec_analysis)} 个技术参数")
        return self.technical_specs
        
    def create_customer_matching_matrix(self) -> List[Dict[str, Any]]:
        """创建客户需求匹配矩阵"""
        logger.info("创建客户需求匹配矩阵...")
        
        matching_matrix = []
        
        # 基于产品数据创建匹配规则
        for product in self.clean_data:
            applications = product.get('applications', [])
            industries = product.get('target_industries', [])
            specs = product.get('technical_specs', {})
            
            for app in applications:
                match_rule = {
                    'customer_need': app,
                    'recommended_product': product['name'],
                    'product_id': product['id'],
                    'key_specs': self._extract_key_specs_for_application(specs, app),
                    'selling_points': product.get('key_selling_points', [])[:3],
                    'target_industries': industries,
                    'match_confidence': self._calculate_match_confidence(product, app)
                }
                matching_matrix.append(match_rule)
                
        # 按匹配度排序
        matching_matrix.sort(key=lambda x: x['match_confidence'], reverse=True)
        
        logger.info(f"创建了 {len(matching_matrix)} 个匹配规则")
        return matching_matrix
        
    def _extract_key_specs_for_application(self, specs: Dict[str, Any], application: str) -> List[str]:
        """为特定应用提取关键规格"""
        key_specs = []
        
        # 根据应用场景确定重要参数
        if '温度' in application.lower():
            for spec_name in ['working_temperature', 'accuracy', 'range']:
                if spec_name in specs:
                    spec_data = specs[spec_name]
                    if isinstance(spec_data, dict):
                        key_specs.append(f"{spec_name}: {spec_data.get('value', '')}")
                        
        elif '距离' in application.lower() or '位移' in application.lower():
            for spec_name in ['detection_range', 'accuracy', 'resolution']:
                if spec_name in specs:
                    spec_data = specs[spec_name]
                    if isinstance(spec_data, dict):
                        key_specs.append(f"{spec_name}: {spec_data.get('value', '')}")
                        
        # 通用重要参数
        for spec_name in ['accuracy', 'range', 'working_temperature']:
            if spec_name in specs and len(key_specs) < 3:
                spec_data = specs[spec_name]
                if isinstance(spec_data, dict):
                    key_specs.append(f"{spec_name}: {spec_data.get('value', '')}")
                    
        return key_specs
        
    def _calculate_match_confidence(self, product: Dict[str, Any], application: str) -> float:
        """计算匹配置信度"""
        confidence = 0.5  # 基础分数
        
        # 根据产品名称匹配度加分
        name = product.get('name', '').lower()
        app_lower = application.lower()
        
        if any(keyword in name for keyword in app_lower.split()):
            confidence += 0.3
            
        # 根据技术规格完整度加分
        specs = product.get('technical_specs', {})
        if len(specs) > 5:
            confidence += 0.1
            
        # 根据描述详细度加分
        description = product.get('description', '')
        if len(description) > 50:
            confidence += 0.1
            
        return min(confidence, 1.0)  # 最大值为1.0