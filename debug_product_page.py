#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
<AUTHOR> 
@license          http://www.178188.xyz
@lastmodify       2025年8月1日
模块说明: 调试产品详情页面
"""

import asyncio
import logging
from playwright.async_api import async_playwright

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def debug_product_page():
    """调试产品详情页面"""
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False)  # 显示浏览器
        page = await browser.new_page()
        
        try:
            # 测试产品详情页面
            urls = [
                "https://www.zhaozhongai.com/product/241.html",  # CPU单元
                "https://www.zhaozhongai.com/product/868.html",  # 总线耦合器
            ]
            
            for url in urls:
                logger.info(f"\n访问产品页面: {url}")
                await page.goto(url, wait_until='networkidle')
                await asyncio.sleep(3)
                
                # 获取页面标题
                title = await page.title()
                logger.info(f"页面标题: {title}")
                
                # 查找PDF链接
                pdf_links = await page.query_selector_all('a[href*=".pdf"]')
                logger.info(f"找到 {len(pdf_links)} 个PDF链接:")
                
                for i, link in enumerate(pdf_links):
                    href = await link.get_attribute('href')
                    text = await link.inner_text()
                    logger.info(f"  PDF {i+1}: {text.strip()} -> {href}")
                
                # 查找下载相关的按钮或链接
                download_selectors = [
                    'a:has-text("下载")',
                    'a:has-text("资料下载")',
                    'a:has-text("立即下载")',
                    '.download',
                    '.btn-download',
                    '[onclick*="download"]'
                ]
                
                logger.info("查找下载按钮:")
                for selector in download_selectors:
                    try:
                        elements = await page.query_selector_all(selector)
                        if elements:
                            logger.info(f"  选择器 '{selector}' 找到 {len(elements)} 个元素")
                            
                            for i, element in enumerate(elements[:3]):
                                text = await element.inner_text()
                                href = await element.get_attribute('href')
                                onclick = await element.get_attribute('onclick')
                                logger.info(f"    元素 {i+1}: '{text}' href='{href}' onclick='{onclick}'")
                    except Exception as e:
                        logger.debug(f"选择器 {selector} 失败: {e}")
                
                # 查找可能包含下载链接的容器
                logger.info("查找下载容器:")
                container_selectors = [
                    '.download-section',
                    '.file-section',
                    '.attachment',
                    '.resources',
                    '.documents'
                ]
                
                for selector in container_selectors:
                    try:
                        elements = await page.query_selector_all(selector)
                        if elements:
                            logger.info(f"  容器 '{selector}' 找到 {len(elements)} 个")
                    except:
                        pass
                
                # 尝试点击可能的下载按钮
                logger.info("尝试点击下载按钮...")
                
                download_buttons = await page.query_selector_all('a:has-text("下载"), button:has-text("下载")')
                
                for i, button in enumerate(download_buttons[:3]):
                    try:
                        text = await button.inner_text()
                        logger.info(f"  尝试点击按钮 {i+1}: '{text}'")
                        
                        await button.click()
                        await asyncio.sleep(3)
                        
                        # 检查是否出现了新的PDF链接
                        new_pdf_links = await page.query_selector_all('a[href*=".pdf"]')
                        if new_pdf_links:
                            logger.info(f"  点击后出现了 {len(new_pdf_links)} 个PDF链接:")
                            for j, link in enumerate(new_pdf_links):
                                href = await link.get_attribute('href')
                                text = await link.inner_text()
                                logger.info(f"    新PDF {j+1}: {text.strip()} -> {href}")
                        
                        # 检查是否跳转到了新页面
                        current_url = page.url
                        if current_url != url:
                            logger.info(f"  页面跳转到: {current_url}")
                            
                            # 在新页面查找PDF链接
                            page_pdf_links = await page.query_selector_all('a[href*=".pdf"]')
                            if page_pdf_links:
                                logger.info(f"  新页面中找到 {len(page_pdf_links)} 个PDF链接:")
                                for k, link in enumerate(page_pdf_links):
                                    href = await link.get_attribute('href')
                                    text = await link.inner_text()
                                    logger.info(f"    页面PDF {k+1}: {text.strip()} -> {href}")
                        
                        break  # 只点击第一个按钮
                        
                    except Exception as e:
                        logger.debug(f"点击按钮失败: {e}")
                        continue
                
                # 保存页面HTML用于分析
                html_content = await page.content()
                filename = f"product_page_{url.split('/')[-1]}.html"
                with open(filename, "w", encoding="utf-8") as f:
                    f.write(html_content)
                logger.info(f"页面HTML已保存: {filename}")
                
                # 检查页面源码中的PDF链接
                import re
                pdf_urls = re.findall(r'https?://[^\s"\']*\.pdf', html_content)
                if pdf_urls:
                    logger.info(f"页面源码中找到PDF URL: {pdf_urls}")
                
                await asyncio.sleep(2)
            
        except Exception as e:
            logger.error(f"调试过程出错: {e}")
            
        finally:
            await browser.close()

if __name__ == "__main__":
    asyncio.run(debug_product_page())
